name: Release Notifications

on:
  push:
    branches:
      - prod
      - preview

permissions:
  statuses: read
  contents: read

jobs:
  wait-for-gcb:
    runs-on: ubuntu-latest
    steps:
      - name: Wait for GCB
        uses: autotelic/action-wait-for-status-check@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          statusName: "Google Cloud Build"
          timeoutSeconds: 1200 # wait up to 20 minutes
          intervalSeconds: 15

  notify:
    name: Send Release Notification
    runs-on: ubuntu-latest
    needs: wait-for-gcb
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v3

      - name: Setup Node.js 22
        uses: actions/setup-node@v3
        with:
          node-version: 22
          cache: npm

      - name: Run Release Notification
        run: node scripts/release-notification.js ${{ github.ref_name }}
