# @fintary/web

## 10.19.10 (2025-07-09 22:08:06)

<details>
<summary>Changes</summary>

### Patch Changes

- deff3bc: - Update TWC agent payout rate calculation to average result.
  - Fix invalid hook call in JsonStringToggle formatter
  - Add loading skeletons to DynamicSelect
  - Set max width in value on FieldConfig component to reduce overflow (still can happen)
  - Don't retry permission denied requests
- 6898cf2: Fix creating / saving commissions failing due to transaction_type formatter returning object.
</details>

<details>
<summary>Previous Versions</summary>

## 10.19.9 (2025-07-09 00:17:30)

<details>
<summary>Changes</summary>

### Patch Changes

- 89fe5b5: Fix the issue where the commission amount couldn’t be removed due to dependency checks, and optimize the loading UI for document type selection.
- 54ec1df: Fix companies using potential_match column that doesn't exist in our schema caused by: 1. Ignoring the potential_match field 2. Reverting deprecated code
- 624c907: Export alignment on Policy page
  - Move config from web to common, and add textFormatter for use in export
  - Move DataTransformation from web to common to be used by textFormatter
  - Refactor the report_data export logic with the following steps: getData → get fields based on config and accountSetting → fetch dynamicSelect data → formatData → export CSV
- 031d8f1: Display the carrier grid level name and the house payout grid level name for single carrier mode comp profiles
- a32cf9b: Include relational fields for params in data update criteria and actions. Avoid using asynchronous logic for executing custom rules.
- c3bd7d7: Fix some issue on processor part: 1.missing data in company and some fields. 2. Crash when open crate a new processors.
- c09430e: - Introduced `policyDataIfEmptyFields` constant to manage fields sourced from policy data.
  - Updated `queryFieldValues` to utilize `findMany` for fetching statement data, enhancing flexibility in selecting fields.
  - Modified `StatementFilterService` to incorporate policy data fields in filter logic.
  - Refactored table formatting in `Statements.tsx` to improve tooltip handling for policy data discrepancies.
- Updated dependencies [db2a894]
- Updated dependencies [624c907]
- Updated dependencies [df70eef]
- Updated dependencies [d519450]
- Updated dependencies [07887a5]
- Updated dependencies [c09430e]
  - common@0.24.4
</details>

## 10.19.8 (2025-07-07 10:37:32)

<details>
<summary>Changes</summary>

### Patch Changes

- 62df1bd: Fix handling of commission basis for is_virutal commission records by not adding child commission_amounts.
</details>

## 10.19.7 (2025-07-07 09:57:19)

<details>
<summary>Changes</summary>

### Patch Changes

- 5b43915: Show multiplier for a multiplier for each comp profile in match criteria when the calculation method is either "Pay commission to grid level" or "Share override to grid level" under enable com grid for setting
- 0a84843: - Move config from web to common, and add textFormatter for use in export.
  - Implemented `getDocumentFieldConfig` to retrieve field configurations based on account mode and timezone.
  - Added `formatExportData` and `mapHeaderFromFieldConfigs` to process export data and headers.
  - Updated the `ExportDocumentsHandler` to include timezone as a parameter.
  - Introduced new constants for field types and labels to improve maintainability.
- Updated dependencies [9e548c2]
- Updated dependencies [0a84843]
  - common@0.24.3
</details>

## 10.19.6 (2025-07-06 04:07:33)

<details>
<summary>Changes</summary>

### Patch Changes

- 7b3e302: Fix file upload API failing due to missing service configuration
- dbfd95e: Update metrics view to fix the issue where the table doesn’t separate "auto" and "manual" data, and to resolve the legend overlap in the ‘Company Documents Count’ chart.
</details>

## 10.19.5 (2025-07-04 22:50:27)

<details>
<summary>Changes</summary>

### Patch Changes

- ab5b67b: Fixed agent transaction save failure when all transactions had been deleted
</details>

## 10.19.4 (2025-07-04 17:56:46)

<details>
<summary>Changes</summary>

### Patch Changes

- 6a7dee2: In admin accounts, sort users by active first. Add user state formatter.
- Updated dependencies [6a7dee2]
  - common@0.24.2
</details>

## 10.19.3 (2025-07-04 06:54:50)

<details>
<summary>Changes</summary>

### Patch Changes

- 86432e1: Support specifying a multiplier for each comp profile in match criteria when the calculation method is either "Pay commission to grid level" or "Share override to grid level".
- 02a0e63: Hotfix the document processing page crash issue
- 316e7d5: Minor improvements to report processor playground and admin view.
- d023076: Show virtual & virtual type in commission data view
</details>

## 10.19.2 (2025-07-03 18:09:48)

<details>
<summary>Changes</summary>

### Patch Changes

- 78c6ede: Fix for search bar not working on multiple app pages.
</details>

## 10.19.1 (2025-07-03 06:25:20)

<details>
<summary>Changes</summary>

### Patch Changes

- 18a97a0: Updated report summary labels
- daa48af: Allow user to select manual grouping calculation method
- fbf82d6: Fix lint issue on companies page.
- 54c1f53: Fix the issue where the page crashes when selecting an existing mapping.
- Updated dependencies [daa48af]
  - common@0.24.1
</details>

## 10.19.0 (2025-07-02 08:45:48)

<details>
<summary>Changes</summary>

### Minor Changes

- 034e734: Update Companies & Global Companies Page: 1. Link Suggestions: Added a feature to suggest potential account companies that can be linked to each global company. It shows how many unlinked companies are available and allows linking them directly. 2. Merge Function: Introduced a merge feature that lets users merge selected fields from one global company into another, helping maintain cleaner and more consistent data.
### Patch Changes

- c75dbd1: Fixed an error that occurred when updating data for the 'state' field
- 31a6bcd: Optimise the Companies / Global Companies page:
  1. Fixed the issue preventing global companies from being updated.
  2. Optimized the global companies search function by removing irrelevant search options.
  3. Improved the UI for processors and profiles in the Companies / Global Companies page by adding item counts and collapse functionality.
- 2c59195: Replace comp reports data source from snapshotdata to accounting transactions in new endpoint and new FE component in '.../comp-reports/report_str_id' route.
- f2b8da2: Trigger multi-company mode when multiple documents are uploaded and no classification results are available. And include company and document type details in the upload preview page. Also align the right of type selector.
- 2c59195: Replace comp reports data source from snapshotdata to accounting transactions part IV
- 9c7a7bf: Allow user to override policy splits
- e971a40: In the past, importing a file would automatically create a new mapping, often resulting in many similar or duplicate mappings. Now, users must now click the ‘Save’ button to confirm whether they want to keep the mapping.
- Updated dependencies [dc66daf]
- Updated dependencies [2c59195]
- Updated dependencies [2c59195]
- Updated dependencies [034e734]
  - common@0.24.0
</details>

## 10.18.0 (2025-07-01 05:15:06)

<details>
<summary>Changes</summary>

### Minor Changes

- 7660655: Replace Select with EnhancedSelect for component ProcessMappingForm
### Patch Changes

- 82abbf2: Enhance the data update tool to support executing queries independently of custom code logic.
</details>

## 10.17.4 (2025-06-30 19:27:16)

<details>
<summary>Changes</summary>

### Patch Changes

- 53ac327: Fixed crash on data actions tool due to new virtual_type field
- 5015a9a: Update front-end and API to support one-to-many carrier relationships.
- a8c926c: Add bulk receivable calc for policy.
</details>

## 10.17.3 (2025-06-28 01:04:43)

<details>
<summary>Changes</summary>

### Patch Changes

- f0e0e59: Updated transaction management on the Agents page
</details>

## 10.17.2 (2025-06-27 16:51:32)

<details>
<summary>Changes</summary>

### Patch Changes

- e3e8c66: Enhanced performance for transaction management on the Agents page
- 7c1e7a0: Replace comp reports data source from snapshotdata to accounting transactions part IV
- Updated dependencies [7c1e7a0]
  - common@0.23.8
</details>

## 10.17.1 (2025-06-27 16:03:26)

<details>
<summary>Changes</summary>

### Patch Changes

- 1b942df: Fix the issue where the document profile’s company disappears due to a sorting issue.
- ca19604: Added a new display-only Excess field to the Policies page, consistent with fields like Target Premium and Annualized Revenue.
</details>

## 10.17.0 (2025-06-27 04:25:17)

<details>
<summary>Changes</summary>

### Minor Changes

- 65a04c6: Add validation of date range when bulk-updating agent carrier grid levels.
- a7ffed0: - Replaced Select with EnhancedSelect in DataBulkAdd and updated the onChange handling
  - Add getBasicQueries to API class to support multi queries, also refactor type for variables.
### Patch Changes

- ced82c3: Optimize the Document Profile page:
  1. Add account company selection and display.
  2. Add document sync functionality to sync documents with related companies.
  3. Add sorting functionality to the Document Profile view.
  4. Add pagination to the Document Profile view.
- 63837a1: Reports now format agency_payout_rates as percentages too.
</details>

## 10.16.3 (2025-06-26 19:28:31)

<details>
<summary>Changes</summary>

### Patch Changes

- a267e86: Refactoring the contacts transactions add - PART 2
- 1cf7578: Fixed agentCommissionPayout calculation on backend.
- Updated dependencies [cf38844]
- Updated dependencies [1cf7578]
  - common@0.23.7
</details>

## 10.16.2 (2025-06-26 07:54:07)

<details>
<summary>Changes</summary>

### Patch Changes

- 6c97bf9: Enabled report processors for all roles.
- Updated dependencies [6c97bf9]
  - common@0.23.6
</details>

## 10.16.1 (2025-06-26 04:21:21)

<details>
<summary>Changes</summary>

### Patch Changes

- 710bafc: Normalized formatting for commissions amount and rates.
- a76f08e: Suggested change
  Refactored payout rate formatter logic and zero-value filtering for commissions page.
- Updated dependencies [710bafc]
- Updated dependencies [a76f08e]
  - common@0.23.5
</details>

## 10.16.0 (2025-06-25 16:02:09)

<details>
<summary>Changes</summary>

### Minor Changes

- 10d636e: Fix an issue that statement amount is null after uploading statement successfully.
</details>

## 10.15.4 (2025-06-25 03:00:44)

<details>
<summary>Changes</summary>

### Patch Changes

- f94d1ce: Refactoring the contacts transactions add - PART 1
- 20f9bf0: Replace comp reports data source from snapshotdata to accounting transactions part III
- Updated dependencies [89aaadc]
- Updated dependencies [20f9bf0]
  - common@0.23.4
</details>

## 10.15.3 (2025-06-24 08:09:30)

<details>
<summary>Changes</summary>

### Patch Changes

- d8f52f3: Document edit modal now shows any valid status.
- 8b237e4: Fixed issue where user could not update processors
- d3acfd1: Fix deselect contacts issue for policy filter
- d2e59ca: Migrated Reconciliations.js to tsx
- 8990322: Updated contacts -> `patchData` to convert `start_date` and `end_date` to Date objects
  Expanded export field configurations to include new fields.
  Introduced new handlers for uplines, downlines, and saved reports in the contact export functionality.
  Updated tests to cover new handlers and ensure correct data formatting.
- 134f822: Added support for select statements based on processing date range when grouping
- fab9042: The Agent Receivable and Override Receivable values should not be multiplied by the split percentage.
- Updated dependencies [d8f52f3]
- Updated dependencies [134f822]
  - common@0.23.3
</details>

## 10.15.2 (2025-06-23 02:31:10)

<details>
<summary>Changes</summary>

### Patch Changes

- 9d270e4: Fixed lint warns
- Updated dependencies [9d270e4]
  - common@0.23.2
</details>

## 10.15.1 (2025-06-21 05:55:46)

<details>
<summary>Changes</summary>

### Patch Changes

- 552070c: Fix application crash that occurs when the resultFormatter method is called but doesn't exist in the Formatter class
- a62f894: Fixed time filter offset for this month
- de2f9bb: Replace comp reports data source from snapshotdata to accounting transactions part II
- 302466b: Implement unselected agents for the dashboard filter
- de2f9bb: Replace comp reports data source from snapshotdata to accounting transactions part I
- Updated dependencies [de2f9bb]
- Updated dependencies [302466b]
  - common@0.23.1
</details>

## 10.15.0 (2025-06-19 20:18:24)

<details>
<summary>Changes</summary>

### Minor Changes

- 370baa2: Implemented BA MOO-specific grouping calculation method
- dff81b3: The whole row of linked commissions should be grayed out.
### Patch Changes

- Updated dependencies [370baa2]
  - common@0.23.0
</details>

## 10.14.1 (2025-06-19 02:50:07)

<details>
<summary>Changes</summary>

### Patch Changes

- a0a3e8a: Implemented a new services for handling AI provider interactions.
  Added abstraction layer to support multiple providers (OpenAI, Grok, Claude).
  Introduced provider-specific configurations and API clients.
  Updated prompt processing logic to be provider-agnostic.
  Added basic usage examples and documentation.
- 31fe4fa: Add relation field Commission → Commission amount to dashboard for Policies
  Remove scrollbar from widget
  Automatically select appropriate formatter based on the selected field
  Update UI: instead of using Policies.customer_name, display as Policies → Customer name
- 11c1020: Fix the issue where reading undefined data when login
- Updated dependencies [a0a3e8a]
  - common@0.22.1
</details>

## 10.14.0 (2025-06-19 01:20:20)

<details>
<summary>Changes</summary>

### Minor Changes

- 2c36d77: RiskTag now can sync member count back to the BenefitPoint
### Patch Changes

- 949fef0: Move time period filter to the widget builder as the filter is only used for the time bucket
- 62f69fb: Replace comp reports data source from snapshotdata to accounting transactions part I
- Updated dependencies [2c36d77]
  - common@0.22.0
</details>

## 10.13.0 (2025-06-18 06:00:21)

<details>
<summary>Changes</summary>

### Minor Changes

- 3f30a1b: The new grouping process generates virtual records based on the grouped items and infers the correct premium amount and commission rate according to the calculation method specified in the grouping rules.
### Patch Changes

- 3f30a1b: This change allows clients to set up grouping rules by specifying on how to filter the data and how the grouping keys are transformed for specific rule
- 986cce6: Updated rate format for "Agent payout rate" column
- Updated dependencies [2aa1fb5]
- Updated dependencies [3f30a1b]
- Updated dependencies [3f30a1b]
  - common@0.21.0
</details>

## 10.12.0 (2025-06-17 07:40:08)

<details>
<summary>Changes</summary>

### Minor Changes

- e54ba48: Add new data tool for bulk updating agent carrier grid levels.
- 1746658: Replace Select with EnhancedSelect in PreviewUpload component.
- 7859287: Fixed issue of missing agent names of agent commission total row on commissions page.
### Patch Changes

- ace26b2: Fix the issue where the new processor resets to default on every action and cannot be created. Also remove the old document profiles logic from the processor page.
- 71030fc: Update the data update preview API and frontend to display commission flags and their log.
- e7d871c: Add 'All' sheet option for multi-sheet spreadsheets to combine all worksheets with source identification, also apply the logic the related part auto/manual document processing.
- 7f5dbaf: Disable selecting startTime > endTime and vice versa in BasicDateRangePicker
Use normalizeFieldValues inside filterFieldValues to avoid duplicate filter values
Select additional report_data_id to display the policy document in reconciliation v1
Fix the where condition to correctly retrieve values for Compensation type v2
When using reconciliation v2, hide the checkboxes to prevent edit and delete actions
</details>

## 10.11.0 (2025-06-13 17:57:14)

<details>
<summary>Changes</summary>

### Minor Changes

- 0783711: Updated bulk CSV/TSV data preview
### Patch Changes

- 592a243: Added new field to the "Commissions" page, "Advanced Amount" to track the "Advanced" column in carriers' statements.
- Updated dependencies [592a243]
  - common@0.20.6
</details>

## 10.10.1 (2025-06-13 06:46:55)

<details>
<summary>Changes</summary>

### Patch Changes

- 97dd128: Show Table -> Show table
- 0b545a3: Hotfix the issue where the page crashes when selecting a new extraction on the “Create new processor” page. Also fix the extraction structure issue, where the result includes unnecessary data and cannot be properly split into Tables and Lines.
- b365633: Add matched result table
Use the CommissionCalcLog component to display the contents instead of raw JSON
Fix the error of expanding all rows in Tests with the same index
</details>

## 10.10.0 (2025-06-12 23:32:27)

<details>
<summary>Changes</summary>

### Minor Changes

- 762e785: Update logic of displaying commission totals at document page
### Patch Changes

- 1d887fd: Report processors - replaced codemirror with fully featured processor playground
- 62fa847: Fix the issue where "data_imports" can't be created due to relational field conflicts, and add "processor_str_id" and "mapping_str_id" fields to "data_imports" during both manual and auto import processes, then display these fields in the data imports view.
- Updated dependencies [1d887fd]
  - common@0.20.5
</details>

## 10.9.2 (2025-06-12 05:20:21)

<details>
<summary>Changes</summary>

### Patch Changes

- aadec40: Allow to update accounting transaction dates from agents view.
- a36e031: Showing the account name instead of the account ID on the Global Companies page, let user easier to identify each account. Also add account filter for Global Companies page.
</details>

## 10.9.1 (2025-06-11 09:39:48)

<details>
<summary>Changes</summary>

### Patch Changes

- 7bdae93: Allow selecting more options beyond ‘Z’ (e.g., ‘AA’, ‘AB’) in the column dropdown for mapping in document processing, and sort all options alphabetically.
- Updated dependencies [8ee096a]
  - common@0.20.4
</details>

## 10.9.0 (2025-06-10 19:59:47)

<details>
<summary>Changes</summary>

### Minor Changes

- 3bbaa23: Fix the issue of showing "(not found in Fintary)" for the columns carrier/mga and paying entity on commissions page.
### Patch Changes

- 6caef09: Add profit and % profit to reconciliation
- b30fba2: Fix newly created date range not showing.
- Updated dependencies [86b5b44]
  - common@0.20.3
</details>

## 10.8.1 (2025-06-10 09:35:04)

<details>
<summary>Changes</summary>

### Patch Changes

- d920336: Add support for importing Fintary format comp grids
</details>

## 10.8.0 (2025-06-10 05:21:34)

<details>
<summary>Changes</summary>

### Minor Changes

- 49139d1: On Documents page, update Commission totals validation to include bank amount in (in addition to commission records and statement amount).
### Patch Changes

- 048751e: Fix for accounting transactions in agent page not showing the total amount correctly.
- Updated dependencies [c80cd7e]
  - common@0.20.2
</details>

## 10.7.0 (2025-06-08 23:29:12)

<details>
<summary>Changes</summary>

### Minor Changes

- 1826608: Replace Select with EnhnacedSelect for the component ReviewerSelector at document processor page.
</details>

## 10.6.2 (2025-06-06 20:42:25)

<details>
<summary>Changes</summary>

### Patch Changes

- e491e88: Add tags field to accounting transaction details in the agents page
- Updated dependencies [e491e88]
  - common@0.20.1
</details>

## 10.6.1 (2025-06-06 18:37:51)

<details>
<summary>Changes</summary>

### Patch Changes

- df2471d: Fixed expected receivable values from commissions when export file
</details>

## 10.6.0 (2025-06-06 16:02:22)

<details>
<summary>Changes</summary>

### Minor Changes

- 566851e: On commissions page, add clickable link to columns that have agent tag names.
- 0c7e4c0: Added mapping support for e2e document processing. Now users can select a mapping under the document profile within the company page. If the uploaded file is a spreadsheet and no appropriate processor is available, the system will run the selected mapping and automatically import the data provided all conditions are met.
### Patch Changes

- 9e184d5: Enhance both the front-end and back-end of the data update configurations to display data update actions and criteria clearly.
- fd659b0: Add models to prompts table
  Update UI prompt table, form
  Remove the profile_str_id field when creating or updating, as it does not exist in the schema
  Change the model used for VertexAI
- Updated dependencies [0c7e4c0]
  - common@0.20.0
</details>

## 10.5.4 (2025-06-05 19:49:44)

<details>
<summary>Changes</summary>

### Patch Changes

- 8f8fb64: Add customer name to reconciled lines in commission data
</details>

## 10.5.3 (2025-06-05 05:31:52)

<details>
<summary>Changes</summary>

### Patch Changes

- e3fd2ef: Upgrade nanoid to v5
</details>

## 10.5.2 (2025-06-05 05:10:30)

<details>
<summary>Changes</summary>

### Patch Changes

- a948b3c: Add a “Custom” criteria method for data actions criteria.
- Updated dependencies [798d6c3]
  - common@0.19.2
</details>

## 10.5.1 (2025-06-04 23:15:56)

<details>
<summary>Changes</summary>

### Patch Changes

- 460fedc: Autofill regress test cases collected into the regression test tool
- b7f7b25: Add new Timezone decorator to extract timezone from request headers, defaulting to 'UTC'
  Apply timezone when formatting dates in the Accounting Transactions table
  Updated constants to include HTTP header for timezone
- 42a55c8: Add log formatter field in data update actions.
- 9d531de: Fix minor issue of select field that doesn't show correct result for searching keyword with extra space at the end
- ced8c14: Check valid date range input at edit form, start date must be before end date
- ea0c277: Fixed agent compensation and agent compensation logs fields not appearing in comp reports
- Updated dependencies [b7f7b25]
- Updated dependencies [ced8c14]
  - common@0.19.1
</details>

## 10.5.0 (2025-06-04 04:02:21)

<details>
<summary>Changes</summary>

### Minor Changes

- c1fdfb5: Implement the new database model for DocumentProfile and its relationships. Updated the related codebase across multiple modules and built a new document profile management view.
  Key changes include:
  1. New DocumentProfile view
  - Allows creating, editing, viewing, and deleting individual document profiles.
  - Supports linking profiles to documents, global companies, mappings, prompts, and various configuration settings.
  - Shows the total document count under each profile and allows accessing special files via links.
  2. Document view updates
  - Enables document selection of an associated DocumentProfile.
  3. Company / Global company view updates
  - Allows associating companies with specific document profiles.
  4. Processor, Processor selector, E2e document processing, Data import, and Mapping page updates
  - Updated relationship logic to support the new DocumentProfile structure.
### Patch Changes

- 721040a: Add support for comma-separated values in CONTAINS and NCONTAINS operators in field matcher when 'Treat as array' option is true.
- ea0462e: Fix crash page when sorting documents by uploaded at
- 9693592: Added reverse comp report approval script to code base
- Updated dependencies [721040a]
- Updated dependencies [c1fdfb5]
- Updated dependencies [9693592]
  - common@0.19.0
</details>

## 10.4.4 (2025-06-03 06:52:24)

<details>
<summary>Changes</summary>

### Patch Changes

- 24b51b1: When selecting a new data source, the data field should default to 'any'
  When the data field is set to 'any', the available aggregation_method options should be limited to Count or CountAccumulate only
  Improve aggregation method and formatter type
- d92cf5c: When saving snapshot_data into saved_report, retrieve all fields from the data source to support view changes in the "Views and Fields" settings.
  Display the fields in the report based on the configuration defined in the settings.
- 78c8c83: Hotfix the issue where the document edit page cannot be saved.
- 8b66187: Add support for setting json fields (like agent_commissions) in data actions tool
- d0ea4bf: Switch the order of the carrier grid level name and the house payout grid level name in comp profile view/edit.
- 064f6c6: Fix pdf rendering issue caused by worker service is not compiled during production build
- Updated dependencies [d92cf5c]
- Updated dependencies [2e487a8]
- Updated dependencies [064f6c6]
  - common@0.18.4
</details>

## 10.4.3 (2025-05-29 18:53:31)

<details>
<summary>Changes</summary>

### Patch Changes

- a8f9d9e: Define widget structure
  Enhance no data handling in Chart and Table widgets
- Updated dependencies [a8f9d9e]
  - common@0.18.3
</details>

## 10.4.2 (2025-05-29 10:07:22)

<details>
<summary>Changes</summary>

### Patch Changes

- 00d3fcc: Replace Promise.all with limitConcurrency in comp grid imports
- 00d3fcc: Enable search for Company selector in Agent arrier/agency levels
- 00d3fcc: Reduce web app update notification from 12 hrs to 4 hrs after an update is available
</details>

## 10.4.1 (2025-05-29 05:39:24)

<details>
<summary>Changes</summary>

### Patch Changes

- Updated dependencies [0044f7f]
  - common@0.18.2
</details>

## 10.4.0 (2025-05-29 04:41:56)

<details>
<summary>Changes</summary>

### Minor Changes

- 35d57af: Replaced the 'Expected Receivables' column with 'Agency Receivable', 'Agent Receivable', and 'Override Receivable' columns for commissions and policies.
### Patch Changes

- d8d9098: Ensure large value of currency is displayed in decimal format, not scientific notation
- e044dbf: Display the agent name with the amount in agent commisssions when agent settings are configured to show agent downline commissions.
  Updated logic for calculating report amounts to prioritize specific agent commissions based on contact ID.
- d01075d: Tweak the document edit dialog by adding highlight to ‘statement_amount’ when it requires verification, and prevent auto-focus on the 'notes' field to avoid unintended focus during save or validation errors.
</details>

## 10.3.4 (2025-05-28 07:46:41)

<details>
<summary>Changes</summary>

### Patch Changes

- 1f58d67: Fix an issue at document group page that when selecting group by quarter/year, clicking open document link will crash
</details>

## 10.3.3 (2025-05-27 08:44:04)

<details>
<summary>Changes</summary>

### Patch Changes

- 51dc80f: Display Multiplier information in Agent commission log when exporting
- 9064362: Add Agent commissions (Agents only) Agent commissions (Sales reps only) for data field
- Updated dependencies [51dc80f]
  - common@0.18.1
</details>

## 10.3.2 (2025-05-27 02:51:45)

<details>
<summary>Changes</summary>

### Patch Changes

- ce76de8: Add a “required” message to the company and type inputs and tweak the UI to prevent users from needing to click “Save” twice.
- 384209d: Added agents agency levels and carrier levels
</details>

## 10.3.1 (2025-05-26 22:54:22)

<details>
<summary>Changes</summary>

### Patch Changes

- dce7789: Fix date filter at document page not working properly and show error when end date is before start date
- 1fbbef2: Update commissions filters order
</details>

## 10.3.0 (2025-05-26 08:04:52)

<details>
<summary>Changes</summary>

### Minor Changes

- 26d347a: Improve ui/ux in Comp grid viewer (collapse columns, date ranges, improve btns)
### Patch Changes

- f511da4: Add the custom_view_name field to Agent settings
  Include the id to support updating Agent settings
  Get the correct current account_role_setting when updating
- 5ff8448: Added tooltip with the available options for the different agent settings
- 67dd206: Changed multiline-text input to codemirror for report processors. Added a few rendering UI fixes as well for the report processor views.
- 103ef32: Persist Comp grid import result errors, improve formatting for success and error results
- 83bac27: Fix for comp grid rates not saving on comp grid viewer for new ordered grid levels
- Updated dependencies [c526b66]
- Updated dependencies [f511da4]
- Updated dependencies [26d347a]
- Updated dependencies [67dd206]
  - common@0.18.0
</details>

## 10.2.3 (2025-05-23 05:24:23)

<details>
<summary>Changes</summary>

### Patch Changes

- e4b9ae8: Add option for include grouped commissions in the comp reports
</details>

## 10.2.2 (2025-05-22 07:15:46)

<details>
<summary>Changes</summary>

### Patch Changes

- e173e48: Limit column width to 500px for the new table
- 0c130ca: Fix wrong date parameter names when opening document url at document group page that has group by value upload, deposit, or processing date
- 4d53798: Migrate all dto to Zod schema
- 20889e6: Data actions tool, enabled negative values for numeric fields in criteria
- 2715ab5: Fix crash on document processing when processors have null names.
- 0a0b174: Fixed bug where the generate comp payout report pane would crash on invalid processing start and end dates.
- 5b0223c: Fix the issue where EnhanceDataView sends a duplicated limit parameter as an array when the client changes the pagination size.
- fb2ff53: Fix result formatter default selection
  Add Sum (Accumulated), Count (Accumulated) for data field filter
- adca466: Remove aggregation method from the widget builder
  Clean up result formatter from widget builder
  Move button to right for widget builder
- Updated dependencies [9cd0c0b]
- Updated dependencies [4d53798]
  - common@0.17.3
</details>

## 10.2.1 (2025-05-21 06:56:06)

<details>
<summary>Changes</summary>

### Patch Changes

- 10d120a: Fixed disabled field when add new grid for company of the level
- 0d88d3f: Tweak the Document page UI:
  1. Add more space to the "Status" column to ensure the ✨ label is always visible.
  2. Update the delete restriction for non-admin accounts: previously, only documents with status "New" could be deleted, now allow deletion unless the status is "Processed".
  3. Restrict editing of the Status field to Fintary admins only, to prevent non-admin users from using it to delete processed documents.
- f4f3256: Re-add the upload source icon to both the Document page and the Admin Document page.
- 893a186: Remove result formatter from widget builder and add default any in the data field selector
- d00b60f: Integration between ui and api for comp profile matcher tools.
</details>

## 10.2.0 (2025-05-19 07:04:29)

<details>
<summary>Changes</summary>

### Minor Changes

- 7952175: Implemented global ordered comp grid levels
</details>

## 10.1.4 (2025-05-19 05:59:23)

<details>
<summary>Changes</summary>

### Patch Changes

- eea9234: Aligned all `ESLint` versions across the workspace and moved the base configuration to the root project.
- 819e6db: Add a 'Blank' option to the carrier select dropdown in the comp profile view.
- Updated dependencies [eea9234]
  - common@0.17.2
</details>

## 10.1.3 (2025-05-19 03:33:36)

<details>
<summary>Changes</summary>

### Patch Changes

- 10351a9: Disable updating web version for development env
- 442592b: Only show "Show duplicates" option for Fintary admins
- fabeb4b: Dashboard: Fix producer selection, enable search, refactor/clean up ui
- Updated dependencies [442592b]
  - common@0.17.1
</details>

## 10.1.2 (2025-05-17 00:10:24)

<details>
<summary>Changes</summary>

### Patch Changes

- 8339cdd: Add comp profile matcher ui
</details>

## 10.1.1 (2025-05-16 03:44:33)

<details>
<summary>Changes</summary>

### Patch Changes

- 2d1b73d: Add all entities into dashboard
Policies
Commissions
Agents
Agent payouts
Customers
</details>

## 10.1.0 (2025-05-16 02:02:45)

<details>
<summary>Changes</summary>

### Minor Changes

- f4256c2: Added report processor capability for custom policies and commissions exports.
### Patch Changes

- b26d504: Fix data actions criteria for payment_date
- Updated dependencies [b112990]
- Updated dependencies [f4256c2]
  - common@0.17.0
</details>

## 10.0.4 (2025-05-15 21:02:38)

<details>
<summary>Changes</summary>

### Patch Changes

- 56914d1: Simplify the widget builder filter, merge group by filter and data field filter together.
</details>

## 10.0.3 (2025-05-14 03:51:00)

<details>
<summary>Changes</summary>

### Patch Changes

- Updated dependencies [4a1ec00]
  - common@0.16.0
</details>

## 10.0.2 (2025-05-14 01:52:06)

<details>
<summary>Changes</summary>

### Patch Changes

- Updated dependencies [b7d5bca]
  - common@0.15.2
</details>

## 10.0.1 (2025-05-13 21:08:17)

<details>
<summary>Changes</summary>

### Patch Changes

- 3d471f8: Use LoadingButton for BasicDialog, fix LoadingButton colors for disabled and loading states, fix creating comp profiles without carriers
</details>

## 10.0.0 (2025-05-13 18:51:25)

<details>
<summary>Changes</summary>

### Major Changes
- 9a902ba: Add e2e tests setup for dev environment
### Patch Changes

- 1091dde: Add a field to select custom_view_name for the agent and handle its display accordingly.
- 56dd937: Update account_role_settings to require custom_view_name and adjust related functionality
  Able to create new account_role_settings in the Views and Fields screen
- 7ed2e81: Limit file name width in document page, merge file and override file columns, show override file below in same row, move (+) icon to end of File column.
- 5311e4f: Update the compensation profile view and form to support both single-carrier and multi-carrier modes.
- Updated dependencies [56dd937]
  - common@0.15.1
</details>

## 9.3.0 (2025-05-12 07:33:01)

<details>
<summary>Changes</summary>

### Minor Changes

- 86323c3: Fix issue of document column not showing name at policies page. Improve policy edit form by using new dropdown select.
</details>

## 9.2.1 (2025-05-12 00:30:02)

<details>
<summary>Changes</summary>

### Patch Changes

- 9c8c251: Add the product type to the data filter for policy
- 419ac0b: Added custom error toasts for SignIn errors (firebase)
- dd287d3: Add support for bar chart and line chart for accumulated value
- 6cb4512: Add a new option that will indicate no date filter to be applied for the widget
- Updated dependencies [8576995]
  - common@0.15.0
</details>

## 9.2.0 (2025-05-08 07:54:09)

<details>
<summary>Changes</summary>

### Minor Changes

- 11e4118: Fixed table action -> more menu in wrong position and improve its ux. Fixed label and value of input overlap at edit form.
- 5537b78: Add new filters to commissions and policies: transaction type and group name
### Patch Changes

- fdfca8e: Fix the issue where companies can’t be selected in multi-company mode, and make the "payment_method" field to "RiskTag" accounts only.
- 53f5de5: Make the link icon for grouped commissions clickable, navigating to the associated commission line item where incl_linked=true.
- 3936393: Fix saving widget layout and widget creator button overlap
- 0136db2: Fix issue where search box is difficult to focus on in Document Admin page.
- 5ed9a3a: Add an "upload_source" field and label to differentiate upload sources: Web, API, and Email.
- Updated dependencies [cc856f2]
- Updated dependencies [5ed9a3a]
  - common@0.14.9
</details>

## 9.1.1 (2025-05-07 16:31:29)

<details>
<summary>Changes</summary>

### Patch Changes

- 5bca96d: Enhanced commission calculation logging by including `Scope` and `policyCommissionsUse` details when calc method is `policyScopeCalcsNew`.
- 2f035ad: Fix the issue where "agent_commission" cannot be imported when the account admin is also an agent.
</details>

## 9.1.0 (2025-05-06 19:04:25)

<details>
<summary>Changes</summary>

### Minor Changes

- 4ed6e77: Check fe env production
- 70f01de: Add test ids for e2e
- cb28d7d: Add processing date and deposit date to filter of documents
### Patch Changes

- 9be6b1d: Exclude records in non-editable states by default when running grouping/deduping and reconciliation with grouping
- 024efbd: Fix issue at report page: Agent column should show name instead of id
- ea7f5ea: Update the classification prediction results to save the original output, including those with confidence scores below 0.9 and type results (for model training purposes).
- ba5a548: Implemented resend user invite feature
- 37c5975: Implemented new date operators (Within, before, after, n days/months/years) for the Data Actions tool.
- Updated dependencies [9be6b1d]
- Updated dependencies [37c5975]
  - common@0.14.8
</details>

## 9.0.2 (2025-05-05 07:59:52)

<details>
<summary>Changes</summary>

### Patch Changes

- fae8cc1: Fix the document upload issue and enhance the UI by adjusting the select box.
- 03e29c5: Fix the Sentry error when method is none and clicking 'Gemini' causes a crash.
- c00c194: Tweak the document processing workflow and resolve the issue where ExtractTable cannot be run during the workflow. Also fix the "split_percentage" validation issue. Implement an "process_method" field to distinguish between automatic and manual processing.
- bdfa82b: Add the group by date, company for Agent Payouts
  Add new Data sources - Contacts: able group by name, data fields: Agents balance
  Fix to display only the contact name, excluding the ID
  Replace AccountingTransactionDetailsService with AccountingTransactionsService
- 281effe: Fix selecting result formatter when creating a widget
- Updated dependencies [c00c194]
- Updated dependencies [a18a4fc]
  - common@0.14.7
</details>

## 9.0.1 (2025-05-01 19:12:55)

<details>
<summary>Changes</summary>

### Patch Changes

- Updated dependencies [724886d]
  - common@0.14.6
</details>

## 9.0.0 (2025-05-01 04:22:55)

<details>
<summary>Changes</summary>

### Major Changes
- c92df54: Can't select multiple value of agent select when doing search in dropdown box
### Patch Changes

- bbf779f: Migrates Transactions Page + Components from JS to TS
- 8618daf: Allow using Shift key to select multiple rows
- c6b4813: Move shared widget definition to access column
- b5d9ced: Remove query to "document_profiles" for non-Fintary admins on documents page
</details>

## 8.3.5 (2025-04-30 16:49:38)

<details>
<summary>Changes</summary>

### Patch Changes

- 7cbee46: Fix error handling in the user manager page.
- Updated dependencies [e92548d]
  - common@0.14.5
</details>

## 8.3.4 (2025-04-30 04:36:41)

<details>
<summary>Changes</summary>

### Patch Changes

- Updated dependencies [7f20a58]
  - common@0.14.4
</details>

## 8.3.3 (2025-04-30 00:02:34)

<details>
<summary>Changes</summary>

### Patch Changes

- Updated dependencies [251eaaa]
  - common@0.14.3
</details>

## 8.3.2 (2025-04-29 05:33:57)

<details>
<summary>Changes</summary>

### Patch Changes

- 2f23b8e: Allow users to specify payment method when uploading documents and will set to corresponding payment_method when syncing to BenefitPoint
- Updated dependencies [2f23b8e]
- Updated dependencies [d2a30ef]
  - common@0.14.2
  - @fintary/api@5.5.1
</details>

## 8.3.1 (2025-04-26 02:13:03)

<details>
<summary>Changes</summary>

### Patch Changes

- bfd60f1: Reset state when data source changes
- 6a64202: Fix data fetching issue of regression test
- Updated dependencies [99762a7]
- Updated dependencies [4aafb01]
- Updated dependencies [1951576]
- Updated dependencies [6a64202]
  - common@0.14.1
  - @fintary/api@5.4.2
</details>

## 8.3.0 (2025-04-25 16:24:04)

<details>
<summary>Changes</summary>

### Minor Changes

- f5ccea9: Add regression test feature in admin tools to validate new commission calc updates on provided use cases
### Patch Changes

- ba1f4cd: Filter agent options based on data from selected date ranges when creating comp reports.
- Updated dependencies [ba1f4cd]
- Updated dependencies [2debfc4]
- Updated dependencies [2190186]
- Updated dependencies [f5ccea9]
  - @fintary/api@5.4.0
  - common@0.14.0
</details>

## 8.2.3 (2025-04-24 17:48:54)

<details>
<summary>Changes</summary>

### Patch Changes

- 7468635: Reduce whitespace on bottom of widget
- 8748657: Fix apply filter by Reconciled
- 3e85e99: Remove the unused "web/common/tools" module and update related dependencies. Add new date types for "findAllDate" and "dateProcessor". Add a new utility function "splitAtPositions", and update the default processor code.
- Updated dependencies [10185e6]
- Updated dependencies [3e85e99]
  - @fintary/api@5.3.1
  - common@0.13.1
</details>

## 8.2.2 (2025-04-24 06:40:32)

<details>
<summary>Changes</summary>

### Patch Changes

- 2fbcea0: Update view & fields behaviour for no setting
- 23a2303: Fix bug of activity log export
- 2afb92f: Fix “Data fields” disappearing when adding one
- dae5c3c: Apply filters to the preview widget to make the previewed widget and generated widget consistent
- f01567a: Invited users onboarding workflow asking new users to edit account info fix.
- Updated dependencies [d86f430]
- Updated dependencies [58a723a]
- Updated dependencies [fa0c936]
- Updated dependencies [2fbcea0]
- Updated dependencies [23a2303]
- Updated dependencies [dae5c3c]
- Updated dependencies [f22c1a1]
- Updated dependencies [a2664b5]
- Updated dependencies [f01567a]
  - @fintary/api@5.3.0
</details>

## 8.2.1 (2025-04-23 05:06:22)

<details>
<summary>Changes</summary>

### Patch Changes

- 6cd6444: Fix the bug of including invalid date for search params
- Updated dependencies [945d223]
- Updated dependencies [d759036]
- Updated dependencies [50115bb]
  - @fintary/api@5.2.1
</details>

## 8.2.0 (2025-04-22 20:47:48)

<details>
<summary>Changes</summary>

### Minor Changes

- 978d9b1: Improve performace of table and edit form, use server pagination for dropdown select
- 43c7b76: Allow to input statement month for documents
### Patch Changes

- Updated dependencies [978d9b1]
- Updated dependencies [43c7b76]
  - @fintary/api@5.2.0
</details>

## 8.1.8 (2025-04-22 07:14:28)

<details>
<summary>Changes</summary>

### Patch Changes

- 857ba2d: Add a new Source to Dashboard: accounting_transaction_details
  Add a company_id and company relation
- Updated dependencies [857ba2d]
- Updated dependencies [dac2c98]
  - @fintary/api@5.1.1
</details>

## 8.1.7 (2025-04-22 05:40:44)

<details>
<summary>Changes</summary>

### Patch Changes

- 728a312: Fix the issue of sso logged user cannot view Reconciliation / Commissions / Policy data
- Updated dependencies [6cf78ad]
- Updated dependencies [c06c609]
- Updated dependencies [728a312]
  - @fintary/api@5.1.0
</details>

## 8.1.6 (2025-04-21 21:17:05)

<details>
<summary>Changes</summary>

### Patch Changes

- a27c373: Fixed an issue where exports from the Reconciliation page did not respect applied filters
- Updated dependencies [8b8b5b8]
- Updated dependencies [6ab2b77]
- Updated dependencies [905cf8a]
- Updated dependencies [e49e5ac]
- Updated dependencies [94488e3]
- Updated dependencies [cf09c82]
- Updated dependencies [bf1607c]
  - @fintary/api@5.0.0
</details>

## 8.1.5 (2025-04-17 20:10:07)

<details>
<summary>Changes</summary>

### Patch Changes

- 847499d: Fix for 0 value in split percentage when editing an agent upline
- Updated dependencies [847499d]
  - @fintary/api@4.8.5
</details>

## 8.1.4 (2025-04-17 19:57:08)

<details>
<summary>Changes</summary>

### Patch Changes

- 01c72e9: Only show "See comp grid rates" menu option if Enable comp grids in setting is true
- Updated dependencies [b79cbf1]
- Updated dependencies [3909884]
  - @fintary/api@4.8.4
</details>

## 8.1.3 (2025-04-17 18:56:26)

<details>
<summary>Changes</summary>

### Patch Changes

- 127f289: Improve web UI security
- 4b3826a: Add bulk edit to documents page
- a92e7b2: Fix for comp grids page being accessible if configured to not be accessible via views and fields
- Updated dependencies [4b3826a]
- Updated dependencies [ae27a2b]
  - @fintary/api@4.8.3
</details>

## 8.1.2 (2025-04-16 19:13:34)

<details>
<summary>Changes</summary>

### Patch Changes

- 80889f0: Fixed commissions linked policy not showing the correct policy str_id
- 9f68a9c: Fixed policies agent payout rate override times 100 issues
- Updated dependencies [9f68a9c]
  - @fintary/api@4.8.2
</details>

## 8.1.1 (2025-04-16 08:46:24)

<details>
<summary>Changes</summary>

### Patch Changes

- c50236d: Add support for multiple actions per criteria meet in data actions tool
- Updated dependencies [a268f04]
- Updated dependencies [0143a6d]
- Updated dependencies [c50236d]
- Updated dependencies [72a92c0]
- Updated dependencies [9c90f17]
  - @fintary/api@4.8.1
</details>

## 8.1.0 (2025-04-16 04:33:18)

<details>
<summary>Changes</summary>

### Minor Changes

- 5f801b7: Add sorting feature for widget builder to be able to sort and select N items based on field
### Patch Changes

- Updated dependencies [da7210c]
- Updated dependencies [5f801b7]
- Updated dependencies [d474f79]
  - @fintary/api@4.8.0
</details>

## 8.0.1 (2025-04-15 06:51:42)

<details>
<summary>Changes</summary>

### Patch Changes

- 90aa6d0: Add a "See comp grid rates" context menu to commission line items
- Updated dependencies [120df69]
- Updated dependencies [74127bc]
- Updated dependencies [a3d62ae]
  - common@0.13.0
  - @fintary/api@4.7.0
</details>

## 8.0.0 (2025-04-14 08:34:07)

<details>
<summary>Changes</summary>

### Major Changes
- 8a455b3: Improve header security
### Minor Changes

- 92e7566: Add global indicator for shared widget
  Update tooltips
  Update the widget setting list to show shared widget with account specific widget
### Patch Changes

- cc0bc20: Fix issue of fetching commissions and add/edit commissions
- 5187ae4: Fix error page caused by missing render function when rendering flags
- Updated dependencies [2b965fe]
- Updated dependencies [cc0bc20]
- Updated dependencies [92e7566]
- Updated dependencies [752b383]
  - common@0.12.0
  - @fintary/api@4.6.0
</details>

## 7.7.0 (2025-04-12 00:25:16)

<details>
<summary>Changes</summary>

### Minor Changes

- fd2bc0d: Extract statement amount from filenames when uploading files.
- 887b478: Skip manually reconciled statements in reconciliation process and show Manual reconciler for manually reconciled data
### Patch Changes

- d843e1d: Include account shortname in email to upload documents.
  Make query chips for document states of Pending upload and Pending review Fintary admin only.
  For end users, show documents in Pending review state as Processing.
  Update uploaded at and imported at to be local time instead of UTC.
- b8e51db: Added clear field operation for data actions tool
- Updated dependencies [d843e1d]
- Updated dependencies [fd2bc0d]
- Updated dependencies [887b478]
- Updated dependencies [b8e51db]
  - @fintary/api@4.5.0
  - common@0.11.0
</details>

## 7.6.1 (2025-04-11 05:51:30)

<details>
<summary>Changes</summary>

### Patch Changes

- d6298df: Update the document import process to allow importing the "agent_commissions", "agent_payout_rate", "agent_commission_payout_rate" and "agent_commission_status" fields. ("agent_commission_payout_rate" and "agent_commission_status" have ambiguous type issues, so please consult with the engineering team before using them.)
  Also, add normalization for them to ensure the values are either "currency" or "percentages". Additionally, implement a checker to verify that the key belongs to the corresponding "agent ID".
- Updated dependencies [d6298df]
- Updated dependencies [a53b76d]
  - @fintary/api@4.4.1
</details>

## 7.6.0 (2025-04-10 17:36:25)

<details>
<summary>Changes</summary>

### Minor Changes

- 9c93924: Add flags column for commissions in ui
- d83c6df: Add a "Pending upload" status for documents created in the "document" table but not yet uploaded to storage, to clarify the specific file status, and add a "Pending upload" status chip and query option in both the document and admin document views.
- d01793d: Allow users to ungroup grouped commission line items
### Patch Changes

- f66031b: Normalize agent_payout_rate and agent_commission_payout_rate data
- 18faa4f: Change activity log filters to a dropdown instead of chips, and add Gmail sync option
- Updated dependencies [9c93924]
- Updated dependencies [f66031b]
- Updated dependencies [18faa4f]
- Updated dependencies [d01793d]
- Updated dependencies [e60b88f]
- Updated dependencies [e5bff8b]
  - @fintary/api@4.3.0
  - common@0.10.0
</details>

## 7.5.2 (2025-04-10 03:17:48)

<details>
<summary>Changes</summary>

### Patch Changes

- c014b8c: Add Policy relations to Data fields
- Updated dependencies [7080ead]
- Updated dependencies [628ce91]
- Updated dependencies [c014b8c]
  - @fintary/api@4.2.11
</details>

## 7.5.1 (2025-04-09 19:22:36)

<details>
<summary>Changes</summary>

### Patch Changes

- c36e9e3: Add commission group alerts for commission that linked with others
- Updated dependencies [c36e9e3]
  - @fintary/api@4.2.10
</details>

## 7.5.0 (2025-04-09 18:58:25)

<details>
<summary>Changes</summary>

### Minor Changes

- 2a41cc9: Allow to search whole text by using double quotes in dropdown select
</details>

## 7.4.0 (2025-04-09 05:04:14)

<details>
<summary>Changes</summary>

### Minor Changes

- 8d5da17: Add drag to scroll back to the table
### Patch Changes

- Updated dependencies [2c1056c]
  - @fintary/api@4.2.7
</details>

## 7.3.0 (2025-04-09 04:43:48)

<details>
<summary>Changes</summary>

### Minor Changes

- 1f93f5d: Replace the implicit '\_companies_processors' table with an intermediate table, and add an 'import_status' field to it. And update both the UI for companies and the admin companies UI to allow users to select the 'import_status'.
### Patch Changes

- Updated dependencies [1f93f5d]
  - common@0.9.3
  - @fintary/api@4.2.6
</details>

## 7.2.5 (2025-04-09 02:07:14)

<details>
<summary>Changes</summary>

### Patch Changes

- a945e00: Use sorting table for widget table
- Updated dependencies [86ee2ec]
- Updated dependencies [10d451a]
  - @fintary/api@4.2.5
</details>

## 7.2.4 (2025-04-08 16:39:43)

<details>
<summary>Changes</summary>

### Patch Changes

- a16ad49: Update aggregator selector
- 31729ec: Fix the issue where saving document edit requires clicking twice.
- 3405452: Optimize the feedback of syncing statements error for RiskTag, including showing statement IDs grouped by company, storing alerts and correctly saving stats.
- 61a3451: Comp grid viewer UI tweaks
- 6b07603: Two decimals formatter fix for BuddyIns account for agent payout rate field.
- Updated dependencies [7bce7ff]
- Updated dependencies [3405452]
- Updated dependencies [8155963]
- Updated dependencies [6b07603]
  - @fintary/api@4.2.4
  - common@0.9.2
</details>

## 7.2.3 (2025-04-07 16:50:16)

<details>
<summary>Changes</summary>

### Patch Changes

- 207e188: Agent payout rate override formatter fix (Fix 100\* off values)
</details>

## 7.2.2 (2025-04-05 01:45:13)

<details>
<summary>Changes</summary>

### Patch Changes

- aac0788: Fix document processing page crash when processor doesn't return valid results.
</details>

## 7.2.1 (2025-04-05 01:26:02)

<details>
<summary>Changes</summary>

### Patch Changes

- 12293e7: Use localized Loader for widget preview instead of global (which blocks whole page)
- Updated dependencies [6274be7]
- Updated dependencies [12293e7]
  - @fintary/api@4.2.1
  - common@0.9.1
</details>

## 7.2.0 (2025-04-05 01:05:09)

<details>
<summary>Changes</summary>

### Minor Changes

- b67f6b1: Remove dynamically generated agent_commission_payout_rate and agent_payout_rate columns and surface underlying fields (allows editing values).
- e61d1a5: Minor fixes and improvements to new table
### Patch Changes

- Updated dependencies [b67f6b1]
  - @fintary/api@4.2.0
</details>

## 7.1.2 (2025-04-04 23:21:34)

<details>
<summary>Changes</summary>

### Patch Changes

- 8624184: Update styles, remove unused dependencies, and update the location in callback
- ddb589c: Add default fields for preview data on data actions tool
- Updated dependencies [739fc03]
- Updated dependencies [8624184]
- Updated dependencies [148e9a0]
  - @fintary/api@4.1.3
</details>

## 7.1.1 (2025-04-03 20:02:13)

<details>
<summary>Changes</summary>

### Patch Changes

- ac77d0d: Optimize manual grouping, improve wording/feedback, include additional fields.
- Updated dependencies [ac77d0d]
  - @fintary/api@4.1.2
</details>

## 7.1.0 (2025-04-03 19:16:59)

<details>
<summary>Changes</summary>

### Minor Changes

- 907e7e0: Add support to data actions tool for Array fields and operators (e.g. tags field)
### Patch Changes

- Updated dependencies [907e7e0]
  - common@0.9.0
  - @fintary/api@4.1.0
</details>

## 7.0.0 (2025-04-03 07:12:14)

<details>
<summary>Changes</summary>

### Major Changes
- d5d99b4: Add index column to table for TransGlobal
### Minor Changes

- c8c1381: Add launch icon for agent box in uplines
### Patch Changes

- 5c5029c: Data fields display name and fields aggregator logic update
- 2786f93: Add a "Bank total" field and include it in the export. Also replace the file path, optimise the upload date type in the export with the filename.
- 7a1a9e0: Fix the issue where the ‘Admin -> document' navigation bar does not work.
- 06bbcfe: Only enable `Bulk sync statements to BenefitPoint` button for RiskTag
- b313d18: Move Gmail sync to Admin > Documents. Remove logout function.
- 6c37761: When opening link to a document's records in commissions/policies pages, use 'document_id' query param instead of 'q', so search is still available.
- f96061e: Widget form validation improvement
- Updated dependencies [de22227]
- Updated dependencies [2786f93]
- Updated dependencies [a327c5f]
- Updated dependencies [45c61b8]
- Updated dependencies [ff85e35]
- Updated dependencies [205b60f]
- Updated dependencies [798ed11]
  - @fintary/api@4.0.2
  - common@0.8.3
</details>

## 6.0.0 (2025-04-02 05:34:40)

<details>
<summary>Changes</summary>

### Major Changes
- e2f6b00: Enhanced table performace, reduce lagging when loading a large set of data
### Minor Changes

- 1c1e2ab: Disable "Calculate comp" in commissions context menu when agent_commissions_status is "Manual", "Approved", "Paid", "Offset", "No payment"
### Patch Changes

- 1b94387: Add index column to table for TransGlobal
- cb17ca0: Add e2e tests for dashboard
- bd6c262: Update the default field on the statement uploading modal to be “Statement amount”
- af56926: In agent commissions log, rename "Agent split" to "Writing agent split" and remove code that updated this in each iteration, so we keep the original split.
- 83ba92e: Show product type in manual reconcile modal
- 59bc916: Allow user to manually group commission line items
- a2fdbe3: Fix dashboard table view decimal point precision issues. Using BigNumber and formatters now.
- Updated dependencies [8b624d3]
- Updated dependencies [00a160a]
- Updated dependencies [af56926]
- Updated dependencies [59bc916]
  - @fintary/api@4.0.1
  - common@0.8.2
</details>

## 5.5.0 (2025-03-31 16:35:33)

<details>
<summary>Changes</summary>

### Minor Changes

- d933fe1: Add document type classification on document uploads
### Patch Changes

- Updated dependencies [87cc1ee]
- Updated dependencies [95e51a5]
  - @fintary/api@4.0.0
</details>

## 5.4.1 (2025-03-30 23:45:31)

<details>
<summary>Changes</summary>

### Patch Changes

- 920a29a: Get rid of reconciliation data source and replaced the widget
- Updated dependencies [920a29a]
  - @fintary/api@3.6.1
  - common@0.8.1
</details>

## 5.4.0 (2025-03-28 21:42:13)

<details>
<summary>Changes</summary>

### Minor Changes

- cdf74b2: Create new commission fields for agent comp calc linked with accounting transactions
- b1d37fd: Add support for bulk comp calculation from commissions page
### Patch Changes

- 1838f59: On policy split import, properly show error message.
  Also add loading indicator when request is processing.
- 6ef9772: Surface grouped policies in manual reconciliation
- 40ac26f: Added all fields to bulk edit, except for commission amount, commission rate and premium
- Updated dependencies [ab14b7a]
- Updated dependencies [cdf74b2]
- Updated dependencies [1ef516d]
- Updated dependencies [9fa449b]
- Updated dependencies [95b51a0]
- Updated dependencies [b1d37fd]
  - @fintary/api@3.6.0
  - common@0.8.0
</details>

## 5.3.0 (2025-03-27 15:19:11)

<details>
<summary>Changes</summary>

### Minor Changes

- c832438: Add agent_payout_rate_override source from policy and alert message of no comp profile set up
### Patch Changes

- Updated dependencies [c832438]
  - @fintary/api@3.5.0
</details>

## 5.2.3 (2025-03-27 15:12:44)

<details>
<summary>Changes</summary>

### Patch Changes

- 5b45471: Similarity match group by
- Updated dependencies [9bed6ec]
- Updated dependencies [5b45471]
- Updated dependencies [271303d]
- Updated dependencies [bcae28c]
- Updated dependencies [5ca133d]
  - @fintary/api@3.4.5
  - common@0.7.2
</details>

## 5.2.2 (2025-03-26 15:57:55)

<details>
<summary>Changes</summary>

### Patch Changes

- 71581a6: Add more fields to aggregation selector
- Updated dependencies [23da6a6]
  - @fintary/api@3.4.3
</details>

## 5.2.1 (2025-03-26 15:51:41)

<details>
<summary>Changes</summary>

### Patch Changes

- 50b7ad9: Add query chips to the Admin->Documents page
- 1bf2726: Add custom code toggle
- 5f1d5ac: Handle widget crash
- Updated dependencies [2070a45]
- Updated dependencies [42a936c]
- Updated dependencies [5f1d5ac]
- Updated dependencies [aa9d112]
- Updated dependencies [66f44f6]
  - @fintary/api@3.4.2
  - common@0.7.1
</details>

## 5.2.0 (2025-03-25 16:32:26)

<details>
<summary>Changes</summary>

### Minor Changes

- 3b411b5: Apply oneCommissionOneAgent with manual reconciliation
### Patch Changes

- 6508575: Retain pagination when editing/saving an item in commissions and policies page.
- db1f83d: Get rid of Policies processing date data source
- Updated dependencies [3b411b5]
- Updated dependencies [db1f83d]
  - @fintary/api@3.4.0
</details>

## 5.1.1 (2025-03-24 21:39:37)

<details>
<summary>Changes</summary>

### Patch Changes

- Updated dependencies [e53c65a]
  - common@0.7.0
  - @fintary/api@3.3.1
</details>

## 5.1.0 (2025-03-24 15:14:21)

<details>
<summary>Changes</summary>

### Minor Changes

- 8b38d1b: Add “Add condition” for all custom methods
### Patch Changes

- Updated dependencies [f0a09d9]
- Updated dependencies [8b38d1b]
  - @fintary/api@3.3.0
</details>

## 5.0.2 (2025-03-24 04:08:11)

<details>
<summary>Changes</summary>

### Patch Changes

- a0f6719: Refresh dashboard when changing tab back to this page
</details>

## 5.0.1 (2025-03-24 02:28:50)

<details>
<summary>Changes</summary>

### Patch Changes

- 1eecb4a: Fix dashboard filter
</details>

## 5.0.0 (2025-03-21 22:15:30)

<details>
<summary>Changes</summary>

### Major Changes
- acc269a: Term of service should not display at onboarding step
- 329e08c: Improve mapping columns ui, save additional column name to mapping data
</details>

## 4.0.0 (2025-03-21 18:32:22)

<details>
<summary>Changes</summary>

### Major Changes
- 09a05b4: Fix missing filter label, enhanced filter performance
### Patch Changes

- 1af5603: Dashboard UX improvements
- Updated dependencies [1af5603]
- Updated dependencies [5b93d46]
  - @fintary/api@3.2.6
</details>

## 3.2.3 (2025-03-21 17:19:54)

<details>
<summary>Changes</summary>

### Patch Changes

- fe63a1a: Added validation to numeric fields in FieldMatcher
- Updated dependencies [e6b12a1]
  - @fintary/api@3.2.5
</details>

## 3.2.2 (2025-03-21 14:11:35)

<details>
<summary>Changes</summary>

### Patch Changes

- Updated dependencies [33e0f17]
  - common@0.6.2
  - @fintary/api@3.2.4
</details>

## 3.2.1 (2025-03-21 02:40:26)

<details>
<summary>Changes</summary>

### Patch Changes

- 78895cc: Add missing apis google configs
- c620c6d: Implement feature for multiple data field calculation
- Updated dependencies [c620c6d]
  - @fintary/api@3.2.1
</details>

## 3.2.0 (2025-03-20 20:06:01)

<details>
<summary>Changes</summary>

### Minor Changes

- 7b3775d: Document group page: added new options for group by, added total row and few small ui/ux improvements
### Patch Changes

- dd6ce5b: Email auto populated when inviting new producer user
- 2ba163e: Fix payment date range query error in commission data view caused by passing Invalid Date string to server
- 81e0aff: Agent payout rate formatter aligned with exports and app view
- Updated dependencies [0e1a221]
- Updated dependencies [2ba163e]
- Updated dependencies [81e0aff]
- Updated dependencies [7b3775d]
  - @fintary/api@3.2.0
  - common@0.6.1
</details>

## 3.1.0 (2025-03-20 09:54:56)

<details>
<summary>Changes</summary>

### Minor Changes

- 4dfd92c: Add auto documents extracting workflow
### Patch Changes

- 95a6cf0: Support Agent production and commissions
  Data field aggregator update
- Updated dependencies [95a6cf0]
  - @fintary/api@3.1.1
</details>

## 3.0.1 (2025-03-19 20:34:55)

<details>
<summary>Changes</summary>

### Patch Changes

- dc5a0f5: Fixed invite new producer user not showing agent name in dropdown.
- b384863: Added decimal field matcher field type
- ec44db6: Fix data update criteria crash when adding/editig without selecting data entity
- 68a917d: Relabeling of data update tools to data actions
</details>

## 3.0.0 (2025-03-19 20:34:55)

<details>
<summary>Changes</summary>

### Major Changes
- 99c3924: Filter improvement
  - Added navigation icon left and right to scroll filter
  - Stick Fields, Add, Export, Save, Bookmark to the right
- 8ed0791: Enhanced website security: whitelist domains the website can connect to and always use https
### Minor Changes

- 8d2ee05: Data update: Allow data actions to set fields based on other fields
### Patch Changes

- Updated dependencies [8d2ee05]
  - common@0.6.0
  - @fintary/api@3.1.0
</details>

## 2.7.0 (2025-03-19 13:45:59)

<details>
<summary>Changes</summary>

### Minor Changes

- 76e90d8: Add checkbox up_to_received_rate
### Patch Changes

- Updated dependencies [76e90d8]
- Updated dependencies [b6b477c]
- Updated dependencies [9fb1fcd]
  - @fintary/api@3.0.0
</details>

## 2.6.1 (2025-03-18 16:21:09)

<details>
<summary>Changes</summary>

### Patch Changes

- ecd089e: Data update group management UI improvement
</details>

## 2.6.0 (2025-03-18 15:34:10)

<details>
<summary>Changes</summary>

### Minor Changes

- 2577eb2: implement getting rates for receivable reconciliation
### Patch Changes

- 99be7b8: Show expected receivables for commission line items
- f951276: Update data update tool to handle rounding when working with numeric operators
- 6aca509: Set 'Company' field to required when uploading documents
- 315a86e: Data update global config UI tweaks
- 4bf49c7: Fix data update tool not updating data
- 0096d75: Add support for matcher method: IS_WRITING_AGENT
- Updated dependencies [d4f32fc]
- Updated dependencies [99be7b8]
- Updated dependencies [f951276]
- Updated dependencies [e5cf40f]
- Updated dependencies [61b3d9c]
- Updated dependencies [315a86e]
- Updated dependencies [a72a995]
- Updated dependencies [2577eb2]
- Updated dependencies [28d8f31]
- Updated dependencies [0096d75]
- Updated dependencies [a558675]
- Updated dependencies [c3de931]
- Updated dependencies [4fe0ba5]
- Updated dependencies [d04bb89]
  - @fintary/api@2.7.0
  - common@0.5.2
</details>

## 2.5.5 (2025-03-13 18:53:44)

<details>
<summary>Changes</summary>

### Patch Changes

- b0daa2c: Added group name to commissions and policies bulk edit, also added search in bulk edit fields dropdown
- 3f4cc8a: Improved FE performance for data update tool and added pagination to preview results.
- 72a65f1: UI tweaks to add agents level component
- Updated dependencies [8ddd6d7]
- Updated dependencies [3f4cc8a]
  - @fintary/api@2.6.5
</details>

## 2.5.4 (2025-03-13 13:21:43)

<details>
<summary>Changes</summary>

### Patch Changes

- c5bb834: Refactor API service to use axios client
</details>

## 2.5.3 (2025-03-13 04:08:15)

<details>
<summary>Changes</summary>

### Patch Changes

- 2e7bbcb: Fixed data update preview tool bug when changing tabs
- 15e9aae: Fix commissions page bulk editing wipe off field value if accidentally selected it
- fb3ede5: Lint without fix when checking on the commit message stage
- c81a596: Data update tools UI/UX small improvements
- 3d10000: lint before running git commit
- Updated dependencies [c1bb228]
- Updated dependencies [fb3ede5]
- Updated dependencies [3d10000]
- Updated dependencies [2995fd5]
- Updated dependencies [32099f7]
- Updated dependencies [fec2b7b]
  - @fintary/api@2.6.3
</details>

## 2.5.2 (2025-03-10 21:25:00)

<details>
<summary>Changes</summary>

### Patch Changes

- 544349d: Support multiple values for 'Contained in/ Not contained in' operators for FieldMatcher
- 3e7f4db: Comp grid viewer fix for not being able to add or edit comp grids and criteria that doesn't have any rates
- Updated dependencies [544349d]
- Updated dependencies [3e7f4db]
  - @fintary/api@2.6.2
</details>

## 2.5.1 (2025-03-10 06:20:30)

<details>
<summary>Changes</summary>

### Patch Changes

- 242f15b: Disable checking when no session is configured and increase checking interval
- 49d5c5b: Fixed crash when selecting group in data update tool for Foundational account
- Updated dependencies [ae1842f]
- Updated dependencies [0cd1cb9]
- Updated dependencies [6e5a617]
  - @fintary/api@2.6.1
</details>

## 2.5.0 (2025-03-06 18:02:35)

<details>
<summary>Changes</summary>

### Minor Changes

- cea4b59: Support session management for each account
### Patch Changes

- 57d93bb: Added error handling to date ranges management dialog
- 850ae3c: The full run option is enabled for TransGlobal worker
- 5ebab33: Removed comp reports payout status from bulk edit
- Updated dependencies [850ae3c]
- Updated dependencies [cea4b59]
  - @fintary/api@2.6.0
</details>

## 2.4.0 (2025-03-05 19:22:45)

<details>
<summary>Changes</summary>

### Minor Changes

- 7705538: Added Update policy with payout rates button to commissions context dropdown
### Patch Changes

- 717960b: Fixed field operators in widget config
- Updated dependencies [7705538]
- Updated dependencies [d2ea7d8]
- Updated dependencies [850017f]
  - @fintary/api@2.5.0
</details>

## 2.3.2 (2025-03-04 00:22:12)

<details>
<summary>Changes</summary>

### Patch Changes

- 26cc906: Show sync id for companies & products & agents & policies
</details>

## 2.3.1 (2025-03-04 00:12:56)

<details>
<summary>Changes</summary>

### Patch Changes

- a62ff16: Access control for dashboard
- Updated dependencies [a62ff16]
  - @fintary/api@2.4.1
</details>

## 2.3.0 (2025-03-02 23:42:39)

<details>
<summary>Changes</summary>

### Minor Changes

- 6996aaa: Show accounting transactions in agent page for producers
### Patch Changes

- 9d66035: fixed operator 'Not contained in' not showing up for compensation type field
- Updated dependencies [122f42d]
- Updated dependencies [6996aaa]
  - @fintary/api@2.4.0
</details>

## 2.2.2 (2025-02-27 20:08:18)

<details>
<summary>Changes</summary>

### Patch Changes

- f3253fc: Show geo state and payment mode data from policies if not existing in commissions data
- Updated dependencies [fa8f40d]
  - @fintary/api@2.3.3
</details>

## 2.2.1 (2025-02-27 19:09:02)

<details>
<summary>Changes</summary>

### Patch Changes

- 8104c84: Fixed operators in fieldMatcher for reconciliers page
</details>

## 2.2.0 (2025-02-27 07:20:21)

<details>
<summary>Changes</summary>

### Minor Changes

- 53dab7c: Performance improvements for data update tool
### Patch Changes

- Updated dependencies [45ea59e]
- Updated dependencies [53dab7c]
  - @fintary/api@2.3.0
</details>

## 2.1.8 (2025-02-26 05:31:39)

<details>
<summary>Changes</summary>

### Patch Changes

- 9b3c355: Added search box to preview fields dropdown in data update tool
- a587eb3: fix company products filter issue caused by passing wrong cids
</details>

## 2.1.7 (2025-02-25 07:30:51)

<details>
<summary>Changes</summary>

### Patch Changes

- Updated dependencies [ca239ed]
- Updated dependencies [5db7d0e]
- Updated dependencies [01946c5]
- Updated dependencies [b60bd1c]
- Updated dependencies [2b6c5eb]
- Updated dependencies [3627cca]
  - @fintary/api@2.2.3
  - common@0.5.1
</details>

## 2.1.6 (2025-02-24 08:51:42)

<details>
<summary>Changes</summary>

### Patch Changes

- 49c07c8: Stacked bar chart toggle table view and export
- Updated dependencies [1237c2f]
- Updated dependencies [eaad083]
  - @fintary/api@2.2.2
</details>

## 2.1.5 (2025-02-21 21:32:12)

<details>
<summary>Changes</summary>

### Patch Changes

- be9a7cc: Producer view for agent
</details>

## 2.1.4 (2025-02-21 21:08:23)

<details>
<summary>Changes</summary>

### Patch Changes

- da241f8: Added group name to available bulk edit fields
- 3b0a7a4: Add field type to comp rule config statement fields for field matcher
- f7d32a1: Auto reload after widget setting change
- Updated dependencies [717c096]
  - @fintary/api@2.1.2
</details>

## 2.1.3 (2025-02-20 19:57:30)

<details>
<summary>Changes</summary>

### Patch Changes

- f097d1f: Auto reload after widget setting change
</details>

## 2.1.2 (2025-02-20 03:15:02)

<details>
<summary>Changes</summary>

### Patch Changes

- 3a0a480: Added error handling in reports summary page
- 342b9ec: Optimise release notes by collapsing details and previous version changes
- Updated dependencies [bb06ce4]
  - @fintary/api@2.1.1
</details>

## 2.1.1 (2025-02-19 06:13:11)

<details>
<summary>Changes</summary>

### Patch Changes

- d4fc752: fix for not being able to clear data in policies using bulk editor
</details>

## 2.1.0 (2025-02-18 20:52:41)

<details>
<summary>Changes</summary>

### Minor Changes

- cd0cdc8: Support 'use policy data' option for commission items in data update tool
### Patch Changes

- b8c3aa5: Feature: enable bulk syncing statements back to BenefitPoint
- e0e7040: Data update tool: Define and show operators based on the type of the selected field
- Updated dependencies [cd0cdc8]
  - common@0.5.0
  - @fintary/api@2.1.0
</details>

## 2.0.0 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Major Changes
- 3a6ec58: Added new comp reports approval workflow and accounting transactions integration
### Patch Changes

- 6f65473: Default url not include params for dashboard
- Updated dependencies [dd89aa4]
- Updated dependencies [6f65473]
- Updated dependencies [3a6ec58]
  - @fintary/api@2.0.0
  - common@0.4.2
</details>

## 1.7.1 (2025-02-14 22:57:27)

<details>
<summary>Changes</summary>

### Patch Changes

- a660d59: Add company auto selector
</details>

## 1.7.0 (2025-02-13 22:38:55)

<details>
<summary>Changes</summary>

### Minor Changes

- bae82d0: Implemented delete individual reports from report groups summary page
### Patch Changes

- Updated dependencies [ec72830]
- Updated dependencies [bae82d0]
  - @fintary/api@1.10.0
</details>

## 1.6.9 (2025-02-13 01:29:52)

<details>
<summary>Changes</summary>

### Patch Changes

- fa56906: Supports bulk reconciling selected statements
- Updated dependencies [f8bb29c]
- Updated dependencies [cf87da8]
  - @fintary/api@1.9.1
</details>

## 1.6.8 (2025-02-11 21:19:19)

<details>
<summary>Changes</summary>

### Patch Changes

- e9ab407: Support for all operators in the data update tool
- 089f3fb: Enable manual reconciliation on editable commissions
- Updated dependencies [e9ab407]
- Updated dependencies [ea72ed9]
  - @fintary/api@1.9.0
  - common@0.4.1
</details>

## 1.6.7 (2025-02-10 01:29:02)

<details>
<summary>Changes</summary>

### Patch Changes

- ad668c8: Fix dashboard shows account data to producers
- Updated dependencies [ad668c8]
  - @fintary/api@1.8.8
</details>

## 1.6.6 (2025-02-08 20:01:44)

<details>
<summary>Changes</summary>

### Patch Changes

- 3dcc429: Logout existing user with sso login
</details>

## 1.6.5 (2025-02-08 19:53:37)

<details>
<summary>Changes</summary>

### Patch Changes

- 99ebe7c: Fix for creating comp reports with Agents->Groups disabled in Views & Fields
</details>

## 1.6.4 (2025-02-05 07:35:04)

<details>
<summary>Changes</summary>

### Patch Changes

- 1c6a8a3: Update sso sign out
</details>

## 1.6.3 (2025-02-02 04:36:11)

<details>
<summary>Changes</summary>

### Patch Changes

- 66f3ef5: Add the method to process HTML files.
</details>

## 1.6.2 (2025-01-30 05:32:50)

<details>
<summary>Changes</summary>

### Patch Changes

- 8a742d5: SSO error handling
- Updated dependencies [8a742d5]
  - @fintary/api@1.8.5
</details>

## 1.6.1 (2025-01-30 00:46:38)

<details>
<summary>Changes</summary>

### Patch Changes

- 85e97b7: Removed synced companies restriction when selecting companies in agents levels field
</details>

## 1.6.0 (2025-01-23 18:44:56)

<details>
<summary>Changes</summary>

### Minor Changes

- 299a16f: Feature: allow manually reconcile in the commission data view
### Patch Changes

- Updated dependencies [299a16f]
  - @fintary/api@1.8.0
  - common@0.4.0
</details>

## 1.5.13 (2025-01-14 10:15:46)

<details>
<summary>Changes</summary>

### Patch Changes

- 4228cad: Support bulk syncing policies for accounts with AgencyIntegrator worker in PolicyData view & CommissionData view
</details>

## 1.5.12 (2025-01-07 08:06:27)

<details>
<summary>Changes</summary>

### Patch Changes

- Updated dependencies [a7e9a95]
  - @fintary/api@1.7.4
  - common@0.3.2
</details>

## 1.5.11 (2025-01-04 08:24:03)

<details>
<summary>Changes</summary>

### Patch Changes

- 55edd31: Integrate with react compiler
</details>

## 1.5.10 (2025-01-03 10:11:51)

<details>
<summary>Changes</summary>

### Patch Changes

- b9bc7b3: Bug fix for dashboard preview
</details>

## 1.5.9 (2024-12-23 04:08:56)

<details>
<summary>Changes</summary>

### Patch Changes

- d26945a: AgencyIntegrator worker now supports syncing specified policies
- Updated dependencies [d26945a]
  - @fintary/api@1.7.1
  - common@0.3.1
</details>

## 1.5.8 (2024-12-20 14:29:38)

<details>
<summary>Changes</summary>

### Patch Changes

- 899a4f6: Refactor dashboard filter
- Updated dependencies [899a4f6]
  - @fintary/api@1.6.2
</details>

## 1.5.7 (2024-12-16 07:22:50)

<details>
<summary>Changes</summary>

### Patch Changes

- Updated dependencies [bdef42d]
  - @fintary/api@1.6.0
  - common@0.3.0
</details>

## 1.5.6 (2024-12-13 20:24:24)

<details>
<summary>Changes</summary>

### Patch Changes

- ef379d6: Widget create preview on layout
- Updated dependencies [ae1330f]
  - @fintary/api@1.5.5
</details>

## 1.5.5 (2024-12-12 08:24:16)

<details>
<summary>Changes</summary>

### Patch Changes

- 5724aa9: Widget create preview on layout
- Updated dependencies [2b8e359]
  - @fintary/api@1.5.3
</details>

## 1.5.4 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- 5832be4: BugFix: fix the issue of not showing sync action correctly, even the contact is not synced
</details>

## 1.5.3 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- d4aee1a: BugFix: validate data range in compProfileAdd component, start date should less than end date
- Updated dependencies [d4aee1a]
  - @fintary/api@1.5.1
</details>

## 1.5.2 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- 5b87fed: Echart height adjustment
</details>

## 1.5.1 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- e351a77: Add search params to url for insight page
- Updated dependencies [8a5ccb0]
  - common@0.2.0
</details>

## 1.5.0 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Minor Changes

- 1cbc8a5: Feat: support multi workers & integrate MAG syncing service
</details>

## 1.4.1 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- c556232: UI fix for the insights filter bar
</details>

## 1.4.0 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Minor Changes

- 667d7c3: Feature: add compensation type condition
### Patch Changes

- Updated dependencies [667d7c3]
  - common@0.1.0
</details>

## 1.3.2 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- 681e06b: Add tags filter to commission page
- Updated dependencies [681e06b]
  - common@0.0.2
</details>

## 1.3.1 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- 2f334ab: Create widget support group by agents
Create filter by tags for TAG
</details>

## 1.3.0 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Minor Changes

- 49a8f9f: Add support for stacked charts (carriers only)
### Patch Changes

- 49a8f9f: Add 'Enable updates' toggle for user impersonation (CSMs exempt)
</details>

## 1.2.1 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- c693840: BugFix: correctly populate commissionable_premium_amount when creating policy from commission_data
</details>

## 1.2.0 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Minor Changes

- a3b04cd: Feature: allow user to reset filters, added clear filter support to EnhancedDataView component
### Patch Changes

- 76fb02a: Add indicator for synced agents
- 73289ab: Allow agents to be removed from synced policies without unsyncing agent split
- 16d6fee: Add clear filter button on EnhancedDataView
</details>

## 1.1.2 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- c968920: BugFix: Fix the issue where the release page was not scrollable
</details>

## 1.1.1 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- 39ad848: Feature: support overriding synced contact_levels & contact_hierarchy
- 7f8eeeb: Fix: decimal comparison failure because of precision
</details>

## 1.1.0 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Minor Changes

- 9872fd1: Initial integration with accounting transactions and agent balance
- 1244d2f: Feature: create policy from commission data
### Patch Changes

- Updated dependencies [ddaac06]
  - common@0.0.1
</details>

## 1.0.1 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- 0becf97: Web version checker:
  Notify user to refresh app when there are newer version
- e461619: Add Admin > Activity log, to view recent activity across accounts.
- b11e9bd: AgencyIntegrator worker enhancement:
  - Enable iterative syncing, can run a full sync by select the full run option
  - Policy syncing doens't need commission data anymore
- Updated dependencies [e461619]
- Updated dependencies [b11e9bd]
  - common@0.0.0
</details>

</details>
