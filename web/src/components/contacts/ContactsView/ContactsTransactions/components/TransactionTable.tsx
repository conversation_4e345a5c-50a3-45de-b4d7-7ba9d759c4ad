import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { uniq } from 'lodash';

import { RowTarged, TransactionTableRowProps } from './TransactionTableRow';
import { TransactionsUpdates } from '../ContactsTransactions';
import { TransactionTableView } from './TransactionTableView';
import { Transaction } from '../api/AccountingTransactionsApi';
import { TransactionTableHelper } from './helpers/transactionTable.helpers';

type Props = {
  children?: never;

  onSubmitTransactionsUpdates: (updates: TransactionsUpdates) => void;
  handleUpdatedTransactions: (transactions: Transaction[]) => void;

  onSubmitUpdatesResult: {
    onLoading: boolean;
    success: boolean;
  };
  isEditMode: boolean;
  showSettledTransactions: boolean;
  transactions: Transaction[];

  updateTransactions: (transactions: Transaction[]) => void;
};

const TransactionTable: React.FC<Props> = (props) => {
  const { transactions, updateTransactions } = props;
  const [rowsOpen, setRowsOpen] = useState<RowsOpenMap>({});
  const [removedRows, setRemovedRows] = useState<RemovedRows>(
    initiRemovedRowsState()
  );
  const [updatedRows, setUpdatedRows] = useState<UpdatedRows>([]);

  const { handleUpdatedTransactions, onSubmitTransactionsUpdates } = props;

  const handleUpdatedRows = useCallback(
    (newUpdatedRows: UpdatedRows, newTransactions?: Transaction[]) => {
      if (Array.isArray(newUpdatedRows)) {
        const updates = uniq([...updatedRows, ...newUpdatedRows]);
        setUpdatedRows(updates);
      }

      if (Array.isArray(newTransactions)) {
        handleUpdatedTransactions(newTransactions);
      }
    },
    [handleUpdatedTransactions, updatedRows]
  );

  const deleteTransactionDetails = useCallback(
    (input: { transactionId: string; transactionDetailId: string }) => {
      const { transactionId, transactionDetailId } = input;

      const result = TransactionTableHelper.deleteTransactionDetail(
        { transactionId, transactionDetailId },
        transactions
      );

      if (result.transactionUpdated) {
        updateTransactions(result.transactions);
        handleUpdatedRows(
          [{ transactionId: result.transactionUpdated.id }],
          result.transactions
        );
      }
    },
    [transactions, updateTransactions, handleUpdatedRows]
  );

  const handleDeletedTransaction = useCallback(
    (transaction: Transaction, newTransactions: Transaction[]) => {
      setRemovedRows((prev) => ({
        totalRowsRemoved: prev.totalRowsRemoved + 1,
        transactions: {
          ...prev.transactions,
          [transaction.id]: {
            totalTransactionDetailsRemoved: transaction.details.length,
          },
        },
      }));

      handleUpdatedRows([{ transactionId: transaction.id }], newTransactions);
    },
    [handleUpdatedRows]
  );

  const deleteTransaction = useCallback(
    (transactionId: string) => {
      if (transactionId) {
        const result = TransactionTableHelper.deleteTransactionById(
          transactionId,
          transactions
        );

        if (result.transactionDeleted) {
          updateTransactions(result.transactions);

          handleDeletedTransaction(
            result.transactionDeleted,
            result.transactions
          );
        }
      }
    },
    [transactions, updateTransactions, handleDeletedTransaction]
  );

  const handleRowDeleted = useCallback(
    ({ transactionId, transactionDetailId }: RowTarged) => {
      const isTransactionDetailsUpdates = transactionId && transactionDetailId;

      if (isTransactionDetailsUpdates) {
        return deleteTransactionDetails({ transactionId, transactionDetailId });
      }

      if (transactionId) {
        return deleteTransaction(transactionId);
      }
    },
    [deleteTransactionDetails, deleteTransaction]
  );

  const handleToggleExpandTransaction = useCallback((transactionId: string) => {
    setRowsOpen((prevState) => {
      const isOpen = !!prevState[transactionId]?.isOpen;

      return {
        ...prevState,
        [transactionId]: { isOpen: !isOpen },
      };
    });
  }, []);

  const addNewDefaultTransactionDetail = useCallback(
    (transactionId: string) => {
      const initialDraft = TransactionTableHelper.buildDraftTransactionValues();

      const result = TransactionTableHelper.addNewTransactionDetail({
        transactions,
        transactionId,
        newTransactionDetail: initialDraft.transactionDetail,
      });

      updateTransactions(result);
    },
    [transactions, updateTransactions]
  );

  const addNewDefaultTransaction = useCallback(() => {
    const initialDraft = TransactionTableHelper.buildDraftTransactionValues();
    updateTransactions([...transactions, initialDraft.transaction]);
  }, [transactions, updateTransactions]);

  const handleSaveRowUpdates: HandleSaveRowUpdates = useCallback(
    (payload) => {
      const { transactionId, transactionDetailId } = payload.rowTarget;
      const isTransactionDetailsUpdates = transactionId && transactionDetailId;

      if (isTransactionDetailsUpdates) {
        const result = TransactionTableHelper.updateTransactionDetails({
          transactions,
          transactionId,
          transactionDetailTarget: {
            ...payload.updates,
            id: transactionDetailId,
          },
        });

        updateTransactions(result.transactionsUpdated);
        handleUpdatedRows(result.updatedRows, result.transactionsUpdated);

        return;
      }

      if (transactionId) {
        const result = TransactionTableHelper.updateTransaction({
          transactions,
          transactionTarget: {
            ...payload.updates,
            id: transactionId,
          },
        });

        updateTransactions(result.updatedTransactions);
        handleUpdatedRows(result.updatedRows, result.updatedTransactions);
        return;
      }
    },
    [transactions, updateTransactions, handleUpdatedRows]
  );

  const handleOnSubmitSaveTransactions = useCallback(() => {
    const updates = TransactionTableHelper.getTransactionsUpdates({
      removedRows,
      transactions,
      updatedRows,
    });
    onSubmitTransactionsUpdates(updates);
  }, [onSubmitTransactionsUpdates, removedRows, transactions, updatedRows]);

  const transactionsToView = useMemo(() => {
    return props.showSettledTransactions
      ? transactions
      : TransactionTableHelper.getNonSettledTransactions(transactions);
  }, [props.showSettledTransactions, transactions]);

  const prevSuccessRef = useRef<boolean>();

  useEffect(() => {
    const wasPreviouslySuccess = prevSuccessRef.current;
    const isNowSuccess = props.onSubmitUpdatesResult.success;

    if (isNowSuccess && !wasPreviouslySuccess) {
      if (updatedRows.length > 0) {
        setUpdatedRows([]);
      }

      if (removedRows.totalRowsRemoved > 0) {
        setRemovedRows(initiRemovedRowsState());
      }

      if (Object.keys(rowsOpen).length > 0) {
        setRowsOpen({});
      }
    }

    prevSuccessRef.current = isNowSuccess;
  }, [
    props.onSubmitUpdatesResult.success,
    removedRows.totalRowsRemoved,
    rowsOpen,
    updatedRows.length,
  ]);

  const enableSaveUpdatesAction =
    updatedRows.length > 0 || removedRows.totalRowsRemoved > 0;

  return (
    <TransactionTableView
      addNewDefaultTransactionDetail={addNewDefaultTransactionDetail}
      handleRowDeleted={handleRowDeleted}
      handleSaveRowUpdates={handleSaveRowUpdates}
      handleToggleExpandTransaction={handleToggleExpandTransaction}
      handleSaveTransactionsUpdates={handleOnSubmitSaveTransactions}
      handleAddTransaction={addNewDefaultTransaction}
      onSubmitTransactionsUpdates={props.onSubmitTransactionsUpdates}
      isEditMode={props.isEditMode}
      removedRowsMap={removedRows}
      rowsOpenMap={rowsOpen}
      transactions={transactionsToView}
      saveTransactionsSubmit={{
        enabled: enableSaveUpdatesAction,
        loading: props.onSubmitUpdatesResult.onLoading,
      }}
    />
  );
};

export { TransactionTable };

/**
 *
 * Type definitions for the TransactionTable
 *
 */
type HandleSaveRowUpdates = TransactionTableRowProps['handleSaveRowUpdates'];

export type RowsOpenMap = {
  [transactionId: string]: { isOpen?: boolean };
};

export type RemovedRows = {
  totalRowsRemoved: number;
  transactions: {
    [transactionId: string]: { totalTransactionDetailsRemoved: number };
  };
};

export type UpdatedRows = Array<{ transactionId: string }>;

const initiRemovedRowsState = (): RemovedRows => ({
  totalRowsRemoved: 0,
  transactions: {},
});
