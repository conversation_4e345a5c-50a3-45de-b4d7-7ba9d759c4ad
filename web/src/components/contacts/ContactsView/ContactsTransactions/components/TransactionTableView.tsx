import React from 'react';
import {
  <PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TablePagination,
  TableRow,
} from '@mui/material';
import { AccountingTransactionsType } from 'common/constants/accounting_transactions';
import { transactionTypes } from 'common/constants';
import LoadingButton from '@mui/lab/LoadingButton';

import { TransactionTableColumns } from './TransactionTableColumns';
import {
  RowTarged,
  TransactionTableRow,
  TransactionTableRowProps,
} from './TransactionTableRow';
import { TransactionDetailTable } from './TransactionDetailTable';
import { TransactionsUpdates } from '../ContactsTransactions';
import { RemovedRows, RowsOpenMap } from './TransactionTable';
import { Transaction } from '../api/AccountingTransactionsApi';

type Props = {
  children?: never;

  addNewDefaultTransactionDetail: (transactionId: string) => void;
  handleRowDeleted: (rowTarget: RowTarged) => void;
  handleSaveRowUpdates: HandleSaveRowUpdates;
  handleToggleExpandTransaction: (transactionId: string) => void;
  handleSaveTransactionsUpdates: VoidFunction;
  handleAddTransaction: VoidFunction;
  onSubmitTransactionsUpdates: (updates: TransactionsUpdates) => void;

  isEditMode: boolean;
  transactions: Transaction[];
  rowsOpenMap: RowsOpenMap;
  removedRowsMap: RemovedRows;
  saveTransactionsSubmit: {
    enabled: boolean;
    loading: boolean;
  };
};

const TransactionTableView: React.FC<Props> = (props) => {
  return (
    <TableContainer>
      <Table>
        <TransactionTableColumns
          isEditMode={props.isEditMode}
          hiddenColumns={['AMOUNT', 'TAGS']}
        />

        <TableBody>
          {props.transactions.map((transaction, index) => {
            const isRowOpen = !!props.rowsOpenMap[transaction.id]?.isOpen;

            const isTrasactionCompReportPaymentType =
              transaction.type === transactionTypes.COMP_REPORT_PAYMENT;

            const { totalTransactionDetailsRemoved } = props.removedRowsMap
              .transactions[transaction.id] || {
              totalTransactionDetailsRemoved: 0,
            };

            return (
              <React.Fragment key={transaction.id}>
                <TransactionTableRow
                  handleRowDeleted={props.handleRowDeleted}
                  handleSaveRowUpdates={props.handleSaveRowUpdates}
                  isRowOpen={isRowOpen}
                  editRowEnabled={props.isEditMode}
                  transactionId={transaction.id}
                  amountCellValue={transaction.amount}
                  amountCellLabel={'Total amount'}
                  amountCellDisabled={true}
                  dateCellValue={transaction.date}
                  expandCellEnabled={
                    transaction.details.length > 0 || props.isEditMode
                  }
                  expandCellIsOpen={isRowOpen}
                  expandCellOnClick={props.handleToggleExpandTransaction}
                  notesCellValue={transaction.notes}
                  statusCellValue={transaction.status}
                  statusCellDisabledEdit={
                    transaction.type === AccountingTransactionsType.COMP_REPORT
                  }
                  referenceCellSavedReport={transaction.savedReport}
                />

                {isRowOpen && (
                  <TableRow key={`${index + transaction.id}`}>
                    <TransactionDetailTable
                      handleAddDetailOnClick={
                        props.addNewDefaultTransactionDetail
                      }
                      handleSaveRowUpdates={props.handleSaveRowUpdates}
                      handleRowDeleted={props.handleRowDeleted}
                      isEditMode={props.isEditMode}
                      isTrasactionCompReportPaymentType={
                        isTrasactionCompReportPaymentType
                      }
                      totalTransactionDetailsNotShown={
                        totalTransactionDetailsRemoved
                      }
                      transactionDetails={transaction.details}
                      transactionId={transaction.id}
                    />
                  </TableRow>
                )}
              </React.Fragment>
            );
          })}

          <TableFooterRow
            saveTransactionsSubmit={props.saveTransactionsSubmit}
            onSaveClick={props.handleSaveTransactionsUpdates}
            isEditMode={props.isEditMode}
            handleAddTransaction={props.handleAddTransaction}
          />
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export { TransactionTableView };

/**
 *
 * Type definitions for the TransactionTable
 *
 */
type HandleSaveRowUpdates = TransactionTableRowProps['handleSaveRowUpdates'];

/**
 *
 * Private components for the TransactionDetailTable
 *
 */

const TableFooterRow: React.FC<{
  children?: never;

  handleAddTransaction: () => void;
  onSaveClick: () => void;
  isEditMode: boolean;
  saveTransactionsSubmit: {
    enabled: boolean;
    loading: boolean;
  };
}> = (props) => {
  return (
    <TableRow>
      <TableCell colSpan={8}>
        <Box
          sx={{
            display: 'flex',
            marginTop: 2,
            marginBottom: 4,
            alignItems: 'center',
            justifyContent: 'space-between',
            maxWidth: 1100,
          }}
        >
          <Box>
            <Button
              onClick={props.handleAddTransaction}
              hidden={!props.isEditMode}
            >
              Add transaction
            </Button>
          </Box>
          <Box>
            {/*
             * TODO: https://linear.app/fintary/issue/PLT-2077
             *
             * Pagination is not implemented yet, so we are hiding it for now.
             * When pagination is implemented, we can remove this condition.
             */}
            {!props && (
              <TablePagination
                rowsPerPageOptions={[25, 50, 75, 100]}
                component="div"
                count={30}
                rowsPerPage={25}
                page={1}
                onPageChange={(e) => {
                  // TODO: https://linear.app/fintary/issue/PLT-2077
                }}
                onRowsPerPageChange={(e) => {
                  // TODO: https://linear.app/fintary/issue/PLT-2077
                }}
              />
            )}
          </Box>
          <Box>
            <LoadingButton
              onClick={props.onSaveClick}
              variant="contained"
              hidden={!props.isEditMode}
              disabled={!props.saveTransactionsSubmit.enabled}
              loading={props.saveTransactionsSubmit.loading}
            >
              Save transactions updates
            </LoadingButton>
          </Box>
        </Box>
      </TableCell>
    </TableRow>
  );
};
