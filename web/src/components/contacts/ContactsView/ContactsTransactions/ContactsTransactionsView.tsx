import React, { useEffect } from 'react';
import {
  Box,
  Checkbox,
  FormControlLabel,
  IconButton,
  Typography,
} from '@mui/material';
import { EditOffOutlined, EditOutlined } from '@mui/icons-material';

import { TransactionTable } from './components/TransactionTable';
import {
  TransactionsSaveUpdatesState,
  TransactionsUpdates,
} from './ContactsTransactions';
import { Transaction } from './api/AccountingTransactionsApi';
import LoadingCircle from '@/components/atoms/LoadingCircle';
import useSnackbar from '@/contexts/useSnackbar';

type ContactsTransactionsAddViewProps = {
  handleEditModeOnClick: VoidFunction;
  handleShowSettlementsOnClick: VoidFunction;
  handleSubmitTransactionsUpdates: (updates: TransactionsUpdates) => void;
  handleUpdatedTransactions: (transactions: Transaction[]) => void;
  updateTransactions: (transactions: Transaction[]) => void;

  isEditModeEnabled: boolean;
  showSettledEnabled: boolean;
  sectionLabel: string;
  showEditModeOption: boolean;
  transactions: {
    data: Transaction[];
    isLoading: boolean;
    hasError: boolean;
  };
  onSubmitUpdatesResult: TransactionsSaveUpdatesState;
};

const ContactsTransactionsView: React.FC<ContactsTransactionsAddViewProps> = (
  props
) => {
  const { showSnackbar } = useSnackbar();
  const { onSubmitUpdatesResult } = props;

  useEffect(() => {
    if (onSubmitUpdatesResult?.success) {
      showSnackbar('Transactions updated successfully', 'success');
    } else if (onSubmitUpdatesResult?.error) {
      const { message, type } = onSubmitUpdatesResult.error;
      showSnackbar(message, type);
    }
  }, [
    onSubmitUpdatesResult.success,
    onSubmitUpdatesResult.error,
    showSnackbar,
  ]);

  if (props.transactions.isLoading) {
    return (
      <Box sx={{ width: '100%', textAlign: 'center', mt: 2 }}>
        <LoadingCircle />
      </Box>
    );
  }

  if (props.transactions.hasError) {
    return (
      <Box sx={{ width: '100%', textAlign: 'center', mt: 2 }}>
        <Typography variant="body2" color="error">
          An error occurred while loading transactions, please try again later.
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', mt: 4, mb: 4 }}>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <Typography variant="subtitle2">{props.sectionLabel}</Typography>

        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-end',
          }}
        >
          <FormControlLabel
            control={
              <Checkbox
                checked={props.showSettledEnabled}
                onClick={props.handleShowSettlementsOnClick}
              />
            }
            label="Show settlements"
            value={''}
          />

          {props.showEditModeOption && (
            <EditMode
              onClick={props.handleEditModeOnClick}
              enabled={props.isEditModeEnabled}
            />
          )}
        </Box>
      </Box>

      <TransactionTable
        isEditMode={props.isEditModeEnabled}
        showSettledTransactions={props.showSettledEnabled}
        transactions={props.transactions.data}
        onSubmitTransactionsUpdates={props.handleSubmitTransactionsUpdates}
        handleUpdatedTransactions={props.handleUpdatedTransactions}
        updateTransactions={props.updateTransactions}
        onSubmitUpdatesResult={{
          onLoading: onSubmitUpdatesResult.onLoading,
          success: onSubmitUpdatesResult.success,
        }}
      />

      {props.transactions.data.length === 0 && (
        <Box sx={{ textAlign: 'center', mt: 2, mb: 2 }}>
          <Typography variant="body2" color="textSecondary">
            No transactions found.
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default ContactsTransactionsView;

/**
 *
 * Private components for the ContactsTransactionsView
 *
 */
const EditMode: React.FC<{
  onClick: VoidFunction;
  enabled?: boolean;
}> = (props) => {
  return (
    <IconButton onClick={props.onClick}>
      {props.enabled ? <EditOffOutlined /> : <EditOutlined />}
    </IconButton>
  );
};
