import { TransactionStatuses } from 'common/globalTypes';
import { AccountingTransactionsType } from 'common/constants/accounting_transactions';

import API from '../../../../../services/API';

export type Transaction = {
  id: string;
  strId?: string;
  amount: string;
  date?: Date;
  status: TransactionStatuses;
  type?: AccountingTransactionsType;
  notes: string;
  contactId?: string;
  savedReport?: {
    id: string;
    strId: string;
    name: string;
    isCompReportPayment?: boolean;
  };
  details: TransactionDetail[];
};

export type TransactionDetail = {
  id: string;
  strId?: string;
  commissionStrId?: string;
  savedReport?: Transaction['savedReport'];
  type?: AccountingTransactionsType;
  amount: string;
  date: Date;
  status: TransactionStatuses;
  notes?: string;
  tags?: string[];
  statementId?: string;
};

type GetBasicQueryWithoutData = Omit<
  ReturnType<typeof API.getBasicQuery>,
  'data'
>;

type UseAgentTransactions = GetBasicQueryWithoutData & {
  data: Transaction[];
};

const useGetAgentTransactions = (agentStrId: string): UseAgentTransactions => {
  const { data: response, ...result } = API.getBasicQuery(
    `accounting/transactions/per-agent?agent_str_id=${agentStrId}`
  );

  if (!response || !Array.isArray(response.data)) {
    return { ...result, data: [] };
  }

  return {
    ...result,
    data: response.data.map((item) => {
      const transactionDetails = Array.isArray(
        item.accounting_transaction_details
      )
        ? item.accounting_transaction_details
        : [];

      const savedReport = item.saved_report
        ? {
            id: item.saved_report_id,
            strId: item.saved_report.str_id,
            name: item.saved_report.name,
          }
        : undefined;

      return {
        id: item.id,
        strId: item.str_id,
        amount: item.amount,
        date: item.date ? new Date(item.date) : undefined,
        status: TransactionStatuses[item.status] || TransactionStatuses.DRAFT,
        type: AccountingTransactionsType[item.type],
        notes: item.notes || '',
        contactId: item.contact_id,
        savedReport,
        details: transactionDetails.map((detail) => ({
          id: detail.id,
          strId: detail.str_id,
          commissionStrId: detail.commission_str_id,
          savedReport: detail.saved_report
            ? {
                id: detail.saved_report.id,
                strId: detail.saved_report.str_id,
                name: detail.saved_report.name,
              }
            : undefined,
          type: AccountingTransactionsType[detail.type],
          amount: detail.amount,
          date: detail.date ? new Date(detail.date) : undefined,
          status: detail.status as TransactionStatuses,
          notes: detail.notes || '',
          tags: detail.tags || [],
          statementId: detail.statement_id,
        })),
      };
    }),
  };
};

const useSaveAgentTransactions = () => {
  return {
    mutation: API.getMutation('accounting/transactions/per-agent', 'POST'),
    convertTransactionsToPayload: (input: {
      agentStrId: string;
      transactions: Transaction[];
    }) => ({
      agent_str_id: input.agentStrId,
      transactions: input.transactions.map((transaction) => ({
        transaction_str_id: transaction.strId,
        date: transaction.date,
        amount: parseFloat(transaction.amount),
        status: transaction.status,
        notes: transaction.notes,
        details: transaction.details.map((detail) => ({
          transaction_detail_str_id: detail.strId,
          date: detail.date,
          amount: parseFloat(detail.amount),
          status: detail.status,
          notes: detail.notes,
          tags: detail.tags || [],
        })),
      })),
    }),
  };
};

export { useGetAgentTransactions, useSaveAgentTransactions };
