import React, { useCallback, useEffect, useState } from 'react';

import ContactsTransactionsView from './ContactsTransactionsView';
import { Roles } from '@/types';
import {
  Transaction,
  useSaveAgentTransactions,
  useGetAgentTransactions,
} from './api/AccountingTransactionsApi';
import { ContactsTransactionsAddHelper } from './helpers/contactsTransactionsAdd.helper';

type ContactsTransactionsAddProps = {
  field: {
    id: string;
    label: string;
  };

  agentStrId: string;
  userRole: Roles | null;
};

export const ContactsTransactions: React.FC<ContactsTransactionsAddProps> = (
  props
) => {
  if (!props.agentStrId) {
    return null;
  }

  const [showSettledEnabled, setShowSettledEnabled] = useState<boolean>(false);
  const [editModeEnabled, setEditModeEnabled] = useState<boolean>(false);
  const [transactions, setTransactions] = useState({
    data: [] as Transaction[],
    isLoading: true,
    hasError: false,
  });
  const [transactionsSaveUpdates, setTransactionsSaveUpdates] =
    useState<TransactionsSaveUpdatesState>(initTransactionsSaveUpdatesState());

  const { mutation: saveTransactionsMutation, convertTransactionsToPayload } =
    useSaveAgentTransactions();

  const fetchResult = useGetAgentTransactions(props.agentStrId);
  const handleEditModeOnClick = useCallback(() => {
    setEditModeEnabled((prevState) => !prevState);
  }, []);

  const handleShowSettlementsOnClick = useCallback(() => {
    setShowSettledEnabled((prevState) => !prevState);
  }, []);

  const handleSubmitTransactionsUpdates = useCallback(
    (updates: TransactionsUpdates): void => {
      setTransactionsSaveUpdates({
        onLoading: true,
        success: false,
        error: undefined,
      });

      const { transactionsUpdated } =
        ContactsTransactionsAddHelper.applyTransactionsUpdates(
          updates,
          transactions.data
        );

      const hasDeletedAllTransactions =
        transactions.data.length === 0 && fetchResult.data.length > 0;

      if (transactionsUpdated.length > 0 || hasDeletedAllTransactions) {
        const payload = convertTransactionsToPayload({
          // TODO: we are sending all transactions, we should only send the updated ones
          // but the API is not ready for that yet, we have to send all transactions
          transactions: transactionsUpdated,
          agentStrId: props.agentStrId,
        });

        saveTransactionsMutation.mutate(payload, {
          onSuccess: () => {
            setTransactionsSaveUpdates({
              success: true,
              onLoading: false,
              error: undefined,
            });
          },
          onError: (error) => {
            const isDisabledUpdates = error.message.includes(
              'Updates are disabled'
            );
            const defaultErrorMessage =
              'An error occurred while saving transactions. Please try again later.';

            const message = isDisabledUpdates
              ? error.message
              : defaultErrorMessage;

            setTransactionsSaveUpdates(() => ({
              success: false,
              onLoading: false,
              error: {
                message,
                type: isDisabledUpdates ? 'warning' : 'error',
              },
            }));
          },
        });

        setTransactions({
          data: transactionsUpdated,
          isLoading: false,
          hasError: false,
        });
      }
    },
    [
      convertTransactionsToPayload,
      props.agentStrId,
      saveTransactionsMutation,
      transactions.data,
      fetchResult.data.length,
    ]
  );

  const handleUpdatedTransactions = useCallback(
    (transactions: Transaction[]) => {
      if (transactions.length === 0) {
        setTransactions((prevState) => ({
          ...prevState,
          data: transactions,
        }));

        if (transactionsSaveUpdates.success || transactionsSaveUpdates.error) {
          setTransactionsSaveUpdates(initTransactionsSaveUpdatesState());
        }
      }
    },
    [transactionsSaveUpdates.error, transactionsSaveUpdates.success]
  );

  const updateTransactions = useCallback((transactions: Transaction[]) => {
    setTransactions((prevState) => {
      return {
        ...prevState,
        data: transactions,
      };
    });
  }, []);

  useEffect(() => {
    const isLoading = fetchResult.isLoading;
    const isError = fetchResult.isError;

    if (
      isLoading !== transactions.isLoading ||
      isError !== transactions.hasError
    ) {
      setTransactions({
        data: isError ? [] : fetchResult.data,
        isLoading: isLoading,
        hasError: isError,
      });
    }
  }, [
    fetchResult.isLoading,
    fetchResult.isError,
    fetchResult.data,
    transactions.data.length,
    transactions.hasError,
    transactions.isLoading,
  ]);

  return (
    <ContactsTransactionsView
      handleEditModeOnClick={handleEditModeOnClick}
      handleShowSettlementsOnClick={handleShowSettlementsOnClick}
      handleSubmitTransactionsUpdates={handleSubmitTransactionsUpdates}
      handleUpdatedTransactions={handleUpdatedTransactions}
      updateTransactions={updateTransactions}
      isEditModeEnabled={editModeEnabled}
      sectionLabel={props.field.label}
      showEditModeOption={!(props.userRole === Roles.PRODUCER)}
      showSettledEnabled={showSettledEnabled}
      transactions={transactions}
      onSubmitUpdatesResult={transactionsSaveUpdates}
    />
  );
};

export type TransactionsUpdates = {
  deleted: Array<{ transactionId: string }>;
  updated: Array<Transaction>;
};

export type TransactionsSaveUpdatesState = {
  onLoading: boolean;
  success: boolean;
  error?: {
    type: 'warning' | 'error';
    message: string;
  };
};

const initTransactionsSaveUpdatesState = (): TransactionsSaveUpdatesState => ({
  onLoading: false,
  success: false,
});
