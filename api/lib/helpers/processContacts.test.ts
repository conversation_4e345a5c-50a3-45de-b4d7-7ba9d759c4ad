import { describe, it, expect } from 'vitest';

import processContactsModule from './processContacts';

describe('getListOfIdAndStrIdItemJoinedFromSameIndex', () => {
  const { getListOfIdAndStrIdItemJoinedFromSameIndex } = processContactsModule;

  it('Should join IDs and str_ids from the same index into an array of objects', () => {
    const result = getListOfIdAndStrIdItemJoinedFromSameIndex({
      allContactStrIds: ['str1', 'str2'],
      allContactIds: [1, 2],
    });

    expect(result).toEqual([
      { strId: 'str1', id: 1 },
      { strId: 'str2', id: 2 },
    ]);
  });

  it('Should handle cases where allContactStrIds is longer than allContactIds', () => {
    const result = getListOfIdAndStrIdItemJoinedFromSameIndex({
      allContactStrIds: ['str1', 'str2', 'str3'],
      allContactIds: [1],
    });

    expect(result).toEqual([
      { strId: 'str1', id: 1 },
      { strId: 'str2', id: undefined },
      { strId: 'str3', id: undefined },
    ]);
  });

  it('Should handle cases where allContactIds is longer than allContactStrIds', () => {
    const result = getListOfIdAndStrIdItemJoinedFromSameIndex({
      allContactStrIds: ['str1'],
      allContactIds: [1, 2, 3],
    });

    expect(result).toEqual([
      { strId: 'str1', id: 1 },
      { strId: undefined, id: 2 },
      { strId: undefined, id: 3 },
    ]);
  });

  it('Should return an empty array if both inputs are empty', () => {
    const result = getListOfIdAndStrIdItemJoinedFromSameIndex({
      allContactStrIds: [],
      allContactIds: [],
    });

    expect(result).toEqual([]);
  });

  it('Should remove duplicate IDs and str_ids before joining', () => {
    const result = getListOfIdAndStrIdItemJoinedFromSameIndex({
      allContactStrIds: ['str1', 'str1', 'str2'],
      allContactIds: [1, 1, 2],
    });

    expect(result).toEqual([
      { strId: 'str1', id: 1 },
      { strId: 'str2', id: 2 },
    ]);
  });
});

describe('extractContactIdsFromCompCalc', () => {
  const { extractContactIdsFromCompCalc } = processContactsModule;

  it('Should extract numeric IDs from comp_calc* related fields', () => {
    const item = {
      comp_calc: { 1: {}, 2: {}, total: {} },
      comp_calc_log: { 3: {}, total: {} },
      comp_calc_status: { 4: {}, total: {} },
    };

    const idList = extractContactIdsFromCompCalc(item);

    expect(idList).toEqual([1, 2, 3, 4]);
  });

  it('Should return an empty array if no comp_calc-related fields exist', () => {
    expect(extractContactIdsFromCompCalc({})).toEqual([]);
  });

  it('Should ignore the "total" key in comp_calc-related fields', () => {
    const item = {
      comp_calc: { total: {} },
      comp_calc_log: { total: {} },
      comp_calc_status: { total: {} },
    };

    const result = extractContactIdsFromCompCalc(item);

    expect(result).toEqual([]);
  });

  it('Should handle cases where comp_calc-related fields contain non-numeric keys', () => {
    const item = {
      comp_calc: { '1': {}, invalid: {}, total: {} },
      comp_calc_log: { '2': {}, anotherInvalid: {}, total: {} },
    };

    const result = extractContactIdsFromCompCalc(item);

    expect(result).toEqual([1, 2]);
  });

  it('Should handle cases where comp_calc-related fields are empty objects', () => {
    const item = {
      comp_calc: {},
      comp_calc_log: {},
      comp_calc_status: {},
    };

    const result = extractContactIdsFromCompCalc(item);

    expect(result).toEqual([]);
  });
});

describe('extractContactStrIdsFromAgentComissions', () => {
  const { extractContactStrIdsFromAgentComissions } = processContactsModule;

  it('Should extract contact string IDs from the "contacts" field', () => {
    const item = {
      contacts: ['str1', 'str2'],
    };

    const result = extractContactStrIdsFromAgentComissions(item);

    expect(result).toEqual(['str1', 'str2']);
  });

  it('Should extract contact string IDs from agent* related fields', () => {
    const item = {
      agent_commissions: { str1: {}, str2: {}, total: {} },
      agent_commissions_log: { str3: {}, total: {} },
      agent_commission_payout_rate: { str5: {}, total: {} },
      agent_commissions_status2: { str6: {}, total: {} },
      agent_payout_rate: { str4: {}, total: {} },
      agent_payout_rate_override: { str7: {}, total: {} },
    };

    const result = extractContactStrIdsFromAgentComissions(item);

    expect(result).toEqual([
      'str1',
      'str2',
      'str3',
      'str4',
      'str5',
      'str6',
      'str7',
    ]);
  });

  it('Should ignore the "total" key in agent* related fields', () => {
    const item = {
      agent_commissions: { total: {} },
      agent_commissions_log: { total: {} },
      agent_payout_rate: { total: {} },
      agent_commission_payout_rate: { total: {} },
      agent_commissions_status2: { total: {} },
      agent_payout_rate_override: { total: {} },
    };

    const result = extractContactStrIdsFromAgentComissions(item);

    expect(result).toEqual([]);
  });

  it('Should handle cases where both "contacts" and agent* related fields are present', () => {
    const item = {
      contacts: ['str1', 'str2'],
      agent_commissions: { str3: {}, total: {} },
      agent_commissions_log: { str4: {}, total: {} },
    };

    const result = extractContactStrIdsFromAgentComissions(item);

    expect(result).toEqual(['str1', 'str2', 'str3', 'str4']);
  });

  it('Should return an empty array if no relevant fields are present', () => {
    const item = {};

    const result = extractContactStrIdsFromAgentComissions(item);

    expect(result).toEqual([]);
  });
});
describe('setEmptyAgentCommissionContactByContactStrId', () => {
  const { setEmptyAgentCommissionContactByContactStrId } =
    processContactsModule;

  it('Should initialize agentCommissionContacts as an empty object if not present', () => {
    const item = {} as any;
    const contactStrIds = ['str1', 'str2'];

    setEmptyAgentCommissionContactByContactStrId(item, contactStrIds);

    expect(item.agentCommissionContacts).toEqual({
      str1: '',
      str2: '',
    });
  });

  it('Should populate agentCommissionContacts with empty strings for each contactStrId', () => {
    const item = { agentCommissionContacts: {} };
    const contactStrIds = ['str1', 'str2', 'str3'];

    setEmptyAgentCommissionContactByContactStrId(item, contactStrIds);

    expect(item.agentCommissionContacts).toEqual({
      str1: '',
      str2: '',
      str3: '',
    });
  });

  it('Should overwrite existing agentCommissionContacts with empty strings for provided contactStrIds', () => {
    const item = { agentCommissionContacts: { str1: 'existingValue' } };
    const contactStrIds = ['str1', 'str2'];

    setEmptyAgentCommissionContactByContactStrId(item, contactStrIds);

    expect(item.agentCommissionContacts).toEqual({
      str1: '',
      str2: '',
    });
  });

  it('Should handle an empty contactStrIds array without modifying agentCommissionContacts', () => {
    const item = { agentCommissionContacts: { str1: 'existingValue' } };
    const contactStrIds: string[] = [];

    setEmptyAgentCommissionContactByContactStrId(item, contactStrIds);

    expect(item.agentCommissionContacts).toEqual({
      str1: 'existingValue',
    });
  });

  it('Should handle cases where item is already populated with other fields', () => {
    const item = { otherField: 'value' };
    const contactStrIds = ['str1', 'str2'];

    setEmptyAgentCommissionContactByContactStrId(item, contactStrIds);

    expect(item).toEqual({
      otherField: 'value',
      agentCommissionContacts: {
        str1: '',
        str2: '',
      },
    });
  });
});
describe('createContactMaps', () => {
  const { createContactMaps } = processContactsModule;

  it('Should create maps for contacts by str_id and id', () => {
    const contacts = [
      {
        id: 1,
        str_id: 'str1',
        first_name: 'John',
        last_name: 'Doe',
        type: 'type1',
      },
      {
        id: 2,
        str_id: 'str2',
        first_name: 'Jane',
        last_name: 'Smith',
        type: 'type2',
      },
    ];

    const { contactMapByStrId, contactMapById } = createContactMaps(contacts);

    expect(contactMapByStrId.get('str1')).toEqual({
      name: 'John Doe',
      type: 'type1',
    });
    expect(contactMapByStrId.get('str2')).toEqual({
      name: 'Jane Smith',
      type: 'type2',
    });

    expect(contactMapById.get(1)).toEqual({ name: 'John Doe', type: 'type1' });
    expect(contactMapById.get(2)).toEqual({
      name: 'Jane Smith',
      type: 'type2',
    });
  });

  it('Should handle contacts with missing first_name or last_name', () => {
    const contacts = [
      { id: 1, str_id: 'str1', first_name: 'John', type: 'type1' },
      { id: 2, str_id: 'str2', last_name: 'Smith', type: 'type2' },
      { id: 3, str_id: 'str3', type: 'type3' },
    ];

    const { contactMapByStrId, contactMapById } = createContactMaps(contacts);

    expect(contactMapByStrId.get('str1')).toEqual({
      name: 'John',
      type: 'type1',
    });
    expect(contactMapByStrId.get('str2')).toEqual({
      name: 'Smith',
      type: 'type2',
    });
    expect(contactMapByStrId.get('str3')).toEqual({
      name: '',
      type: 'type3',
    });
    expect(contactMapById.get(1)).toEqual({
      name: 'John',
      type: 'type1',
    });
    expect(contactMapById.get(2)).toEqual({
      name: 'Smith',
      type: 'type2',
    });
    expect(contactMapById.get(3)).toEqual({
      name: '',
      type: 'type3',
    });
  });

  it('Should handle an empty contacts array', () => {
    const { contactMapByStrId, contactMapById } = createContactMaps([]);

    expect(contactMapByStrId.size).toBe(0);
    expect(contactMapById.size).toBe(0);
  });

  it('Should handle duplicate contacts by overwriting with the latest entry', () => {
    const contacts = [
      {
        id: 1,
        str_id: 'str1',
        first_name: 'John',
        last_name: 'Doe',
        type: 'type1',
      },
      {
        id: 1,
        str_id: 'str1',
        first_name: 'Johnny',
        last_name: 'Doe',
        type: 'type2',
      },
    ];

    const { contactMapByStrId, contactMapById } = createContactMaps(contacts);

    expect(contactMapByStrId.get('str1')).toEqual({
      name: 'Johnny Doe',
      type: 'type2',
    });
    expect(contactMapById.get(1)).toEqual({
      name: 'Johnny Doe',
      type: 'type2',
    });
  });
});
describe('setCompCalcContactMapById', () => {
  const { setCompCalcContactMapById } = processContactsModule;

  it('Should populate compCalcContactMapById with contact details from contactMapById', () => {
    const item = {
      comp_calc: { 1: {}, 2: {}, total: {} },
      comp_calc_log: { 3: {}, total: {} },
      comp_calc_status: { 4: {}, total: {} },
    };

    const contactMapById = new Map([
      [1, { name: 'John Doe', type: 'type1' }],
      [2, { name: 'Jane Smith', type: 'type2' }],
      [3, { name: 'Alice Johnson', type: 'type3' }],
    ]);

    setCompCalcContactMapById(item, contactMapById);

    expect((item as any).compCalcContactMapById).toEqual({
      1: { name: 'John Doe', type: 'type1' },
      2: { name: 'Jane Smith', type: 'type2' },
      3: { name: 'Alice Johnson', type: 'type3' },
    });
  });

  it('Should skip IDs not present in contactMapById', () => {
    const item = {
      comp_calc: { 1: {}, 2: {}, total: {} },
      comp_calc_log: { 3: {}, total: {} },
    };

    const contactMapById = new Map([[1, { name: 'John Doe', type: 'type1' }]]);

    setCompCalcContactMapById(item, contactMapById);

    expect((item as any).compCalcContactMapById).toEqual({
      1: { name: 'John Doe', type: 'type1' },
    });
  });

  it('Should handle cases where comp_calc* related fields are empty', () => {
    const item = {
      comp_calc: {},
      comp_calc_log: {},
      comp_calc_status: {},
    };

    const contactMapById = new Map([[1, { name: 'John Doe', type: 'type1' }]]);

    setCompCalcContactMapById(item, contactMapById);

    expect((item as any).compCalcContactMapById).toBeUndefined();
  });

  it('Should ignore the "total" key in comp_calc* related fields', () => {
    const item = {
      comp_calc: { total: {} },
      comp_calc_log: { total: {} },
      comp_calc_status: { total: {} },
    };

    const contactMapById = new Map([[1, { name: 'John Doe', type: 'type1' }]]);

    setCompCalcContactMapById(item, contactMapById);

    expect((item as any).compCalcContactMapById).toBeUndefined();
  });

  it('Should not modify the item if no matching contact IDs are found', () => {
    const item = {
      comp_calc: { 5: {}, 6: {}, total: {} },
    };

    const contactMapById = new Map([
      [1, { name: 'John Doe', type: 'type1' }],
      [2, { name: 'Jane Smith', type: 'type2' }],
    ]);

    setCompCalcContactMapById(item, contactMapById);

    expect((item as any).compCalcContactMapById).toBeUndefined();
  });
});

describe('populateContactDetails', () => {
  const { populateContactDetails } = processContactsModule;

  it('Should populate contactNames and contactNamesWithType from contacts', async () => {
    const item = {
      contacts: ['str1', 'str2'],
      agentCommissionContacts: {},
    };

    const contactMapByStrId = new Map([
      ['str1', { name: 'John Doe', type: 'type1' }],
      ['str2', { name: 'Jane Smith', type: 'type2' }],
    ]);

    await populateContactDetails(item, contactMapByStrId);

    expect((item as any).contactNames).toEqual(['John Doe', 'Jane Smith']);
    expect((item as any).contactNamesWithType).toEqual([
      { name: 'John Doe', type: 'type1' },
      { name: 'Jane Smith', type: 'type2' },
    ]);
  });

  it('Should populate agentCommissionContacts with contact names', async () => {
    const item = {
      agentCommissionContacts: { str1: '', str2: '' },
    };

    const contactMapByStrId = new Map([
      ['str1', { name: 'John Doe', type: 'type1' }],
      ['str2', { name: 'Jane Smith', type: 'type2' }],
    ]);

    await populateContactDetails(item, contactMapByStrId);

    expect(item.agentCommissionContacts).toEqual({
      str1: 'John Doe',
      str2: 'Jane Smith',
    });
  });

  it('Should handle missing contacts in contactMapByStrId', async () => {
    const item = {
      contacts: ['str1', 'str3'],
      agentCommissionContacts: { str1: '', str3: '' },
    };

    const contactMapByStrId = new Map([
      ['str1', { name: 'John Doe', type: 'type1' }],
    ]);

    await populateContactDetails(item, contactMapByStrId);

    expect((item as any).contactNames).toEqual(['John Doe']);
    expect((item as any).contactNamesWithType).toEqual([
      { name: 'John Doe', type: 'type1' },
    ]);
    expect(item.agentCommissionContacts).toEqual({
      str1: 'John Doe',
      str3: '',
    });
  });

  it('Should handle an empty contacts array', async () => {
    const item = {
      contacts: [],
      agentCommissionContacts: {},
    };

    const contactMapByStrId = new Map();

    await populateContactDetails(item, contactMapByStrId);

    expect((item as any).contactNames).toEqual([]);
    expect((item as any).contactNamesWithType).toEqual([]);
    expect(item.agentCommissionContacts).toEqual({});
  });

  it('Should populate documentName from item.document.filename', async () => {
    const item = {
      contacts: ['str1'],
      agentCommissionContacts: {},
      document: { filename: 'document.pdf' },
    };

    const contactMapByStrId = new Map([
      ['str1', { name: 'John Doe', type: 'type1' }],
    ]);

    await populateContactDetails(item, contactMapByStrId);

    expect((item as any).documentName).toBe('document.pdf');
  });

  it('Should handle missing document field gracefully', async () => {
    const item = {
      contacts: ['str1'],
      agentCommissionContacts: {},
    };

    const contactMapByStrId = new Map([
      ['str1', { name: 'John Doe', type: 'type1' }],
    ]);

    await populateContactDetails(item, contactMapByStrId);

    expect((item as any).documentName).toBeUndefined();
  });
});
