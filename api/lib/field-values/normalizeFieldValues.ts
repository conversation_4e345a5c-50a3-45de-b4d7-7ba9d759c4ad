import { DEFAULT_FILTER } from 'common/constants';

const normalizeFieldValues = <T>(
  values: (T | null | '')[]
): (T | typeof DEFAULT_FILTER.BLANK_OPTION)[] => {
  const uniqueValues = new Set(
    values.map((val) => {
      return val === null || val === '' ? DEFAULT_FILTER.BLANK_OPTION : val;
    })
  );

  const sortedValues = Array.from(uniqueValues).sort((a: any, b: any) =>
    a === DEFAULT_FILTER.BLANK_OPTION
      ? -1
      : b === DEFAULT_FILTER.BLANK_OPTION
        ? 1
        : typeof a === 'string'
          ? a.localeCompare(b)
          : a - b
  );

  return sortedValues;
};

export default normalizeFieldValues;
