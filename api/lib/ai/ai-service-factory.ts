import { AIModel } from 'common/constants/prompt';

import { AIService } from './base-ai-service';
import { ClaudeService } from './claude-service';
import { GeminiService } from './gemini-service';
import { GrokService } from './grok-service';
import { LlamaIndexService } from './llama-index-service';
import { OpenAIService } from './openai-service';
import { AIServiceConfig } from './types';

/**
 * Factory for creating and managing AI services
 */
export class AIServiceFactory {
  private static instance: AIServiceFactory;
  private services: Map<AIModel, AIService> = new Map();
  private configs: Map<AIModel, AIServiceConfig> = new Map();

  private constructor() {}

  /**
   * Get the singleton instance of AIServiceFactory
   */
  public static getInstance(): AIServiceFactory {
    if (!AIServiceFactory.instance) {
      AIServiceFactory.instance = new AIServiceFactory();
    }
    return AIServiceFactory.instance;
  }

  /**
   * Register configuration for a specific AI service
   * @param serviceType Type of AI service
   * @param config Configuration for the service
   */
  public registerServiceConfig(
    serviceType: AIModel,
    config: AIServiceConfig
  ): void {
    this.configs.set(serviceType, config);
  }

  /**
   * Get or create an AI service instance
   * @param serviceType Type of AI service to get
   * @returns Instance of the requested AI service
   */
  public async getService(serviceType: AIModel): Promise<AIService> {
    // Return existing service if already initialized
    if (this.services.has(serviceType)) {
      const service = this.services.get(serviceType);
      if (service && service.isInitialized()) {
        return service;
      }
    }

    // Create and initialize new service
    const config = this.configs.get(serviceType);
    if (!config) {
      throw new Error(
        `No configuration registered for service type: ${serviceType}`
      );
    }

    const service = this.createService(serviceType);
    await service.initialize(config);
    this.services.set(serviceType, service);

    return service;
  }

  /**
   * Create a new instance of the specified AI service
   * @param serviceType Type of AI service to create
   * @returns New instance of the requested AI service
   */
  private createService(serviceType: AIModel): AIService {
    switch (serviceType) {
      case AIModel.GEMINI:
        return new GeminiService();
      case AIModel.CHATGPT:
        return new OpenAIService();
      case AIModel.CLAUDE:
        return new ClaudeService();
      case AIModel.GROK:
        return new GrokService();
      case AIModel.LLAMA_INDEX:
        return new LlamaIndexService();
      default:
        throw new Error(`Unsupported service type: ${serviceType}`);
    }
  }
}
