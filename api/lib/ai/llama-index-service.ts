import * as Sentry from '@sentry/nextjs';

import { BaseAIService } from './base-ai-service';
import {
  DEFAULT_MAX_TOKEN,
  DEFAULT_TEMPERATURE,
  DEFAULT_TOP_P,
} from './constants';
import { AIRequestOptions, AIResponseData, AIServiceConfig } from './types';

export class LlamaIndexService extends BaseAIService {
  private apiKey: string;
  private defaultModel = 'llama-index-default';
  private apiUrl: string;

  constructor(config?: AIServiceConfig) {
    super(config);
  }

  async initialize(config: AIServiceConfig): Promise<void> {
    await super.initialize(config);

    if (!this.config.apiKey) {
      throw new Error('LlamaIndex service requires an API key');
    }

    // LlamaIndex typically requires a custom endpoint
    if (!this.config.apiUrl) {
      throw new Error('LlamaIndex service requires an API URL');
    }

    this.apiKey = this.config.apiKey;
    this.apiUrl = this.config.apiUrl as string;
    this.initialized = true;
  }

  async processRequest(options: AIRequestOptions): Promise<AIResponseData> {
    this.validateConfig();

    const model = options.model || this.config.model || this.defaultModel;

    // LlamaIndex typically uses a different request format
    // This is a simplified version and may need to be adjusted based on the specific LlamaIndex API
    const requestBody = {
      model,
      query: options.text,
      file_data: options.inlineData ? options.inlineData.data : undefined,
      file_type: options.inlineData ? options.inlineData.mimeType : undefined,
      max_tokens:
        options.maxTokens || this.config.maxTokens || DEFAULT_MAX_TOKEN,
      temperature:
        options.temperature || this.config.temperature || DEFAULT_TEMPERATURE,
      top_p: options.topP || this.config.topP || DEFAULT_TOP_P,
    };

    try {
      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          `LlamaIndex API error: ${errorData.error?.message || response.statusText}`
        );
      }

      const data = await response.json();
      return {
        content: data.response || data.content || JSON.stringify(data),
        raw: data,
      };
    } catch (error) {
      Sentry.captureException(error);
      throw new Error(`LlamaIndex request failed: ${error.message}`);
    }
  }

  getServiceName(): string {
    return 'LlamaIndex';
  }
}
