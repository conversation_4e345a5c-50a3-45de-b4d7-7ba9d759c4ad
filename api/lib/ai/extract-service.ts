export class ExtractService {
  /**
   * Fetch PDF data from an external service
   * @param filePath Path to the PDF file
   * @returns Processed data from the PDF
   */
  static async fetchPdfData(fileData: Buffer): Promise<any> {
    const formData = new FormData();
    const blob = new Blob([fileData], {
      type: 'application/pdf',
    });
    formData.append('pdf_file', blob);
    formData.append('max_num_pages', '50');
    formData.append('do_table_structure', 'true');
    formData.append('accurate_mode', 'false');

    try {
      const response = await fetch(
        'https://docling-17151875941.us-central1.run.app/process_pdf',
        {
          method: 'POST',
          body: formData,
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          `PDF processing error: ${errorData.error?.message || response.statusText}`
        );
      }

      return await response.json();
    } catch (error) {
      throw new Error(`Failed to fetch PDF data: ${error.message}`);
    }
  }
}
