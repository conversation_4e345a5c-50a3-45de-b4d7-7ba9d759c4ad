import * as Sentry from '@sentry/nextjs';

import { BaseAIService } from './base-ai-service';
import {
  DEFAULT_MAX_TOKEN,
  DEFAULT_TEMPERATURE,
  DEFAULT_TOP_P,
} from './constants';
import { AIRequestOptions, AIResponseData, AIServiceConfig } from './types';

export class GrokService extends BaseAIService {
  private apiKey: string;
  private defaultModel = 'grok-3';
  private apiUrl = 'https://api.x.ai/v1/chat/completions';

  constructor(config?: AIServiceConfig) {
    super(config);
  }

  async initialize(config: AIServiceConfig): Promise<void> {
    await super.initialize(config);

    if (!this.config.apiKey) {
      throw new Error('Grok service requires an API key');
    }

    this.apiKey = this.config.apiKey;
    this.initialized = true;
  }

  async processRequest(options: AIRequestOptions): Promise<AIResponseData> {
    this.validateConfig();

    const model = options.model || this.config.model || this.defaultModel;
    const messages = [];

    // Add system message if needed
    messages.push({
      role: 'user',
      content: [],
    });

    if (options.text) {
      messages[0].content.push({
        type: 'text',
        text: options.text,
      });
    }

    // Note: As of current implementation, Grok may have limited support for inline data/images
    // This implementation may need to be updated as Grok's API evolves

    // Add image content if available
    if (options.inlineData && options.inlineData.data) {
      const mimeType = options.inlineData.mimeType || 'application/pdf';
      messages[0].content.push({
        type: 'image_url',
        image_url: {
          url: `data:${mimeType};base64,${options.inlineData.data}`,
        },
      });
    }

    const requestBody = {
      model,
      messages,
      max_tokens:
        options.maxTokens || this.config.maxTokens || DEFAULT_MAX_TOKEN,
      temperature:
        options.temperature || this.config.temperature || DEFAULT_TEMPERATURE,
      top_p: options.topP || this.config.topP || DEFAULT_TOP_P,
      response_format: { type: 'json_object' },
    };

    try {
      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          `Grok API error: ${errorData.error?.message || response.statusText}`
        );
      }

      const data = await response.json();
      return {
        content: data.choices[0].message.content,
        raw: data,
      };
    } catch (error) {
      Sentry.captureException(error);
      throw new Error(`Grok request failed: ${error.message}`);
    }
  }

  getServiceName(): string {
    return 'Grok';
  }
}
