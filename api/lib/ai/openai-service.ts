import * as Sentry from '@sentry/nextjs';

import { BaseAIService } from './base-ai-service';
import {
  DEFAULT_MAX_TOKEN,
  DEFAULT_TEMPERATURE,
  DEFAULT_TOP_P,
} from './constants';
import { AIRequestOptions, AIResponseData, AIServiceConfig } from './types';

export class OpenAIService extends BaseAIService {
  private apiKey: string;
  private defaultModel = 'gpt-4.1';
  private apiUrl = 'https://api.openai.com/v1/chat/completions';
  private filesApiUrl = 'https://api.openai.com/v1/files';

  constructor(config?: AIServiceConfig) {
    super(config);
  }

  async initialize(config: AIServiceConfig): Promise<void> {
    await super.initialize(config);

    if (!this.config.apiKey) {
      throw new Error('OpenAI service requires an API key');
    }

    this.apiKey = this.config.apiKey;
    this.initialized = true;
  }

  async processRequest(options: AIRequestOptions): Promise<AIResponseData> {
    this.validateConfig();

    const model = options.model || this.config.model || this.defaultModel;
    const messages = [];

    // Add system message if needed
    messages.push({
      role: 'user',
      content: [],
    });

    // Add text content
    if (options.text) {
      messages[0].content.push({
        type: 'text',
        text: options.text,
      });
    }

    // Add image content if available
    if (options.inlineData && options.inlineData.data) {
      const fileId = await this.uploadFile(
        options.inlineData.data,
        options.inlineData.fileName,
        options.inlineData.mimeType
      );

      messages[0].content.push({
        type: 'file',
        file: {
          file_id: fileId,
        },
      });
    }

    const requestBody = {
      model,
      messages,
      max_tokens:
        options.maxTokens || this.config.maxTokens || DEFAULT_MAX_TOKEN,
      temperature:
        options.temperature || this.config.temperature || DEFAULT_TEMPERATURE,
      top_p: options.topP || this.config.topP || DEFAULT_TOP_P,
      response_format: { type: 'json_object' },
    };

    try {
      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          `OpenAI API error: ${errorData.error?.message || response.statusText}`
        );
      }

      const data = await response.json();
      return {
        content: data.choices[0].message.content,
        raw: data,
      };
    } catch (error) {
      Sentry.captureException(error);
      throw new Error(`OpenAI request failed: ${error.message}`);
    }
  }

  async uploadFile(
    fileData: string,
    fileName: string,
    mimeType: string
  ): Promise<string> {
    const formData = new FormData();
    const blob = new Blob([Buffer.from(fileData, 'base64')], {
      type: mimeType,
    });
    formData.append('file', blob, fileName);
    formData.append('purpose', 'user_data');

    try {
      const response = await fetch(this.filesApiUrl, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          `File upload error: ${errorData.error?.message || response.statusText}`
        );
      }

      const data = await response.json();
      return data.id; // Return the file ID
    } catch (error) {
      throw new Error(`File upload failed: ${error.message}`);
    }
  }

  getServiceName(): string {
    return 'ChatGPT';
  }
}
