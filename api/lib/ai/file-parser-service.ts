import { DEFAULT_PROMPT, AIModel } from 'common/constants/prompt';
import { nanoid } from 'nanoid';

import { storage } from '@/lib/firebase-admin';
import prisma from '@/lib/prisma';
import { AIServiceFactory } from './ai-service-factory';
import { ExtractService } from './extract-service';
import {
  AIRequestOptions,
  DocumentData,
  ExtractionResult,
  ParserResult,
} from './types';

/**
 * Service for parsing files using AI services
 */
export class FileParserService {
  private aiServiceFactory: AIServiceFactory;

  constructor() {
    this.aiServiceFactory = AIServiceFactory.getInstance();
  }

  /**
   * Parse a document using the specified AI service
   * @param serviceType Type of AI service to use
   * @param documentId ID of the document to parse
   * @param accountId Account ID
   * @param uid User ID
   * @param options Additional options for the AI request
   * @returns Extraction result
   */
  public async parseDocument(
    serviceTypes: AIModel[],
    documentId: string | number,
    accountId: string,
    uid: string,
    options: {
      prompt?: string;
      promptId?: number;
      extractStrId?: string;
      forceRun?: boolean;
      fileType?: string;
    } = {}
  ): Promise<ParserResult[]> {
    const {
      prompt,
      promptId,
      extractStrId,
      forceRun = false,
      fileType = 'application/pdf',
    } = options;

    const existingExtractions: ParserResult[] = [];

    // Check if extraction already exists
    if (extractStrId && !forceRun) {
      for (const serviceType of serviceTypes) {
        const existingExtraction = await prisma.extractions.findFirst({
          where: { method: serviceType, str_id: extractStrId },
        });

        if (existingExtraction) {
          existingExtractions.push({
            model: serviceType,
            result: {
              data: JSON.parse(existingExtraction.output),
              extraction: {
                id: existingExtraction.id,
                str_id: existingExtraction.str_id,
                method: existingExtraction.method,
              },
            },
          });
        }
      }
    }

    if (existingExtractions.length === serviceTypes.length) {
      return existingExtractions;
    }

    const document = await prisma.documents.findFirst({
      where: { id: documentId, account_id: accountId },
    });

    if (!document) {
      throw new Error('Document not found');
    }

    const filePath = document.file_path || document.override_file_path;
    if (!filePath) {
      throw new Error('No file path found for document');
    }

    const fileRef = storage.file(filePath);
    const [exists] = await fileRef.exists();

    if (!exists) {
      throw new Error('File not found in storage');
    }

    const fileData = (await fileRef.download())[0];
    const fileDataBase64 = fileData.toString('base64');
    let extractedData = null;
    const isPdf = fileType === 'application/pdf';

    if (isPdf) {
      // Try to fetch PDF data using ExtractService
      // If it fails, use the file data directly
      try {
        extractedData = await ExtractService.fetchPdfData(fileData);
      } catch (error) {
        console.error('Error fetching PDF data:', error);
      }
    }

    // Prepare request options
    const requestOptions: AIRequestOptions = {
      text:
        (prompt || DEFAULT_PROMPT) +
        (extractedData ? `\n\n${JSON.stringify(extractedData)}` : ''),
      inlineData: extractedData
        ? undefined
        : {
            mimeType: fileType,
            data: fileDataBase64,
            fileName: document.filename || document.override_filename,
          },
    };

    const results = await Promise.allSettled(
      serviceTypes.map(async (serviceType): Promise<ParserResult> => {
        const existingExtraction = existingExtractions.find(
          (extraction) => extraction.model === serviceType
        );

        if (existingExtraction) {
          return existingExtraction;
        }

        // Get AI service and process request
        const aiService = await this.aiServiceFactory.getService(serviceType);
        const response = await aiService.processRequest(requestOptions);

        // Create document data
        const documentData: DocumentData = {
          document_id: documentId,
          account_id: accountId,
          uid: uid,
          extract_str_id: extractStrId,
          prompt_id: promptId,
        };

        // Save extraction result
        const extraction = await this.createOrUpdateExtractData(
          documentData,
          serviceType,
          response.content
        );

        return {
          model: serviceType,
          result: {
            data: response.content,
            extraction: {
              id: extraction.id,
              str_id: extraction.str_id,
              method: extraction.method,
            },
          },
        };
      })
    );

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      }

      return {
        model: serviceTypes[index],
        error: 'Can not parse document',
      };
    });
  }

  /**
   * Create or update extraction data in the database
   * @param documentData Document data
   * @param serviceType Type of AI service used
   * @param output Output from the AI service
   * @returns Extraction result
   */
  private async createOrUpdateExtractData(
    documentData: DocumentData,
    serviceType: AIModel,
    output: string
  ): Promise<ExtractionResult> {
    const { document_id, account_id, uid, extract_str_id, prompt_id } =
      documentData;

    let result = null;

    // Check if extraction exists
    const exist = await prisma.extractions.findFirst({
      where: { method: serviceType, str_id: extract_str_id },
    });

    if (!extract_str_id) {
      // Create new extraction
      result = await prisma.extractions.create({
        data: {
          document_id,
          account_id,
          uid,
          method: serviceType,
          output,
          result: 'Success',
          result_id: nanoid(),
          str_id: nanoid(),
        },
      });

      // Update extractId to prompt if prompt_id is provided
      if (prompt_id) {
        await prisma.prompts.update({
          where: { id: prompt_id },
          data: {
            extract_str_id: result.str_id,
          },
        });
      }
    } else {
      // Update existing extraction
      if (!exist?.id) {
        console.log(`Extraction id not found, exist: ${!!exist}`);
        throw new Error(
          'Extraction id should be defined, investigation required!'
        );
      }

      result = await prisma.extractions.update({
        where: { id: exist.id },
        data: {
          output,
        },
      });
    }

    return result;
  }
}
