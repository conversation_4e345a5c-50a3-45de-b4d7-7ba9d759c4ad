# @fintary/api

## 6.10.15 (2025-07-11 04:45:48)

<details>
<summary>Changes</summary>

### Patch Changes

- a5c1d82: Remove page & limit to export full data
</details>

<details>
<summary>Previous Versions</summary>

## 6.10.14 (2025-07-11 00:55:30)

<details>
<summary>Changes</summary>

### Patch Changes

- 8ea82da: Removed accounting transactions enabled flag when creating comp reports
- 720496e: Remove users with deleted access from Admin > Accounts
</details>

## 6.10.13 (2025-07-10 18:35:18)

<details>
<summary>Changes</summary>

### Patch Changes

- 5e15322: Sync transaction type , group name and sales vps for DMI account
</details>

## 6.10.12 (2025-07-09 22:08:06)

<details>
<summary>Changes</summary>

### Patch Changes

- deff3bc: - Update TWC agent payout rate calculation to average result.
  - Fix invalid hook call in JsonStringToggle formatter
  - Add loading skeletons to DynamicSelect
  - Set max width in value on FieldConfig component to reduce overflow (still can happen)
  - Don't retry permission denied requests
- a3980f0: Allow users to reset payment allocations
- 534f1dc: Disable member count syncing feature for risk tag
- 6898cf2: Fix creating / saving commissions failing due to transaction_type formatter returning object.
</details>

## 6.10.11 (2025-07-09 00:17:30)

<details>
<summary>Changes</summary>

### Patch Changes

- 54ec1df: Fix companies using potential_match column that doesn't exist in our schema caused by: 1. Ignoring the potential_match field 2. Reverting deprecated code
- 624c907: Export alignment on Policy page
  - Move config from web to common, and add textFormatter for use in export
  - Move DataTransformation from web to common to be used by textFormatter
  - Refactor the report_data export logic with the following steps: getData → get fields based on config and accountSetting → fetch dynamicSelect data → formatData → export CSV
- 031d8f1: Display the carrier grid level name and the house payout grid level name for single carrier mode comp profiles
- 3f63d9b: Fix the commission calculation failure with different calc basis
- de7e04e: When manually adjusting agent_commissions in commissions page, also adjust accounting transaction details
- df70eef: - Introduced a new model `uploaded_saved_reports` to manage file metadata.
  - Updated existing `saved_reports` model to include a foreign key reference to `uploaded_saved_reports`.
  - Added new endpoints for uploading, downloading, listing and filtering saved report files.
  - Created DTOs for file upload and retrieval operations.
- a32cf9b: Include relational fields for params in data update criteria and actions. Avoid using asynchronous logic for executing custom rules.
- bad9e8d: Add a new reconciled status: Commission received
- c5c689a: Fixes for comp reports not being approved
- c3bd7d7: Fix some issue on processor part: 1.missing data in company and some fields. 2. Crash when open crate a new processors.
- d519450: Refactor reconciliation_data v2 api
  - Remove paging when querying the database
  - Implement sorting and pagination in JS
  - Add tests for sorting and pagination
  - Implemented batch retrieval instead of retrieving all records in a single query
- 07887a5: Fix grouping failure caused by null premium_amount & split_percent
- 74e9761: Fix the prisma select issue
- c09430e: - Introduced `policyDataIfEmptyFields` constant to manage fields sourced from policy data.
  - Updated `queryFieldValues` to utilize `findMany` for fetching statement data, enhancing flexibility in selecting fields.
  - Modified `StatementFilterService` to incorporate policy data fields in filter logic.
  - Refactored table formatting in `Statements.tsx` to improve tooltip handling for policy data discrepancies.
- 6b19d3f: Fix for accounting transaction details not saving from agents page
- Updated dependencies [db2a894]
- Updated dependencies [624c907]
- Updated dependencies [df70eef]
- Updated dependencies [d519450]
- Updated dependencies [07887a5]
- Updated dependencies [c09430e]
  - common@0.24.4
</details>

## 6.10.10 (2025-07-07 10:37:32)

<details>
<summary>Changes</summary>

### Patch Changes

- 62df1bd: Fix handling of commission basis for is_virutal commission records by not adding child commission_amounts.
</details>

## 6.10.9 (2025-07-07 09:57:19)

<details>
<summary>Changes</summary>

### Patch Changes

- 9e548c2: - Updated the `is_saved_report` parameter in `shouldGetSelectedFields` to accept a string instead of a boolean.
  - Adjusted related function calls across various API endpoints to pass the `is_saved_report` as a string.
  - Removed unnecessary comments and TODOs from the codebase for clarity.
  - Introduced `ChildRelationshipWithContact` type for better type safety.
- 0a84843: - Move config from web to common, and add textFormatter for use in export.
  - Implemented `getDocumentFieldConfig` to retrieve field configurations based on account mode and timezone.
  - Added `formatExportData` and `mapHeaderFromFieldConfigs` to process export data and headers.
  - Updated the `ExportDocumentsHandler` to include timezone as a parameter.
  - Introduced new constants for field types and labels to improve maintainability.
- Updated dependencies [9e548c2]
- Updated dependencies [0a84843]
  - common@0.24.3
</details>

## 6.10.8 (2025-07-07 02:54:29)

<details>
<summary>Changes</summary>

### Patch Changes

- 401322d: Only calculate renewal profiles if the statement’s compensation type is Renewal Commission.
- 9722a24: Improves performance by minimizing database load for all required data in comp calc.
</details>

## 6.10.7 (2025-07-06 06:51:15)

<details>
<summary>Changes</summary>

### Patch Changes

- 8b49c3a: Fix for auto populate house rate and rate when creating new lines in comp grid viewer for grids with only carrier rate.
- ebd2e55: Fixed bug for data actions tool where a deleted action still takes effect on data preview
</details>

## 6.10.6 (2025-07-06 04:07:33)

<details>
<summary>Changes</summary>

### Patch Changes

- 884fab0: Fix issue where created_by is missing when uploading a document.
- e93c5d8: Fix companies unable to update document profile mappings due to unique constraint violation in "companies_document_profiles".
- 7b3e302: Fix file upload API failing due to missing service configuration
- dbfd95e: Update metrics view to fix the issue where the table doesn’t separate "auto" and "manual" data, and to resolve the legend overlap in the ‘Company Documents Count’ chart.
</details>

## 6.10.5 (2025-07-04 22:50:27)

<details>
<summary>Changes</summary>

### Patch Changes

- ab5b67b: Fixed agent transaction save failure when all transactions had been deleted
</details>

## 6.10.4 (2025-07-04 17:56:46)

<details>
<summary>Changes</summary>

### Patch Changes

- b6aca2a: Reduce classification confidence threshold to 0.8 to allow more files result.
- 6a7dee2: In admin accounts, sort users by active first. Add user state formatter.
- 12d760f: Fixed integration tests
- c925d4a: `Total premium` now available in saved report group views and exports.
- Updated dependencies [6a7dee2]
  - common@0.24.2
</details>

## 6.10.3 (2025-07-04 07:57:16)

<details>
<summary>Changes</summary>

### Patch Changes

- 4bd2560: Not calc receivables for sale reps.
</details>

## 6.10.2 (2025-07-04 06:54:50)

<details>
<summary>Changes</summary>

### Patch Changes

- 86432e1: Support specifying a multiplier for each comp profile in match criteria when the calculation method is either "Pay commission to grid level" or "Share override to grid level".
- e85592c: Delete accounting transactions data from agents payload in agents view
</details>

## 6.10.1 (2025-07-03 06:25:20)

<details>
<summary>Changes</summary>

### Patch Changes

- 18a97a0: Updated report summary labels
- daa48af: Allow user to select manual grouping calculation method
- fe07088: Fixed "Hide commissions with payout" filter on Commissions page that was not returning expected results
- Updated dependencies [daa48af]
  - common@0.24.1
</details>

## 6.10.0 (2025-07-02 08:45:48)

<details>
<summary>Changes</summary>

### Minor Changes

- 034e734: Update Companies & Global Companies Page: 1. Link Suggestions: Added a feature to suggest potential account companies that can be linked to each global company. It shows how many unlinked companies are available and allows linking them directly. 2. Merge Function: Introduced a merge feature that lets users merge selected fields from one global company into another, helping maintain cleaner and more consistent data.
### Patch Changes

- c75dbd1: Fixed an error that occurred when updating data for the 'state' field
- 438f1cb: Correctly populate fields value into generated grouped statement, including new_commission_rate, contacts, split_percentage, agent_commission_payout_rate and agent_payout_rate
- dc66daf: Made export agent columns CSV and added Formatter.label
- 31a6bcd: Optimise the Companies / Global Companies page:
  1. Fixed the issue preventing global companies from being updated.
  2. Optimized the global companies search function by removing irrelevant search options.
  3. Improved the UI for processors and profiles in the Companies / Global Companies page by adding item counts and collapse functionality.
- 2c59195: Replace comp reports data source from snapshotdata to accounting transactions in new endpoint and new FE component in '.../comp-reports/report_str_id' route.
- c6b8ebd: Support running groupings with rules event with no default grouping settings
- 320204a: Fix for comp calc not getting results for Hitchings account
- 2c59195: Replace comp reports data source from snapshotdata to accounting transactions part IV
- e63ac1e: Fix the bulk edit issue that when we update the any field the date field(like deposit date) will be auto updated
- 129de78: Fix the bug of paying current agent if hierarchy processing is none
  Fix the bug of associate current agents with the appropriate compensation profile when the "Apply to Downlines" option is selected for an upline agent in the comp profile settings.
  Fetch all comp profiles for comp profile matching service
- Updated dependencies [dc66daf]
- Updated dependencies [2c59195]
- Updated dependencies [2c59195]
- Updated dependencies [034e734]
  - common@0.24.0
</details>

## 6.9.5 (2025-07-01 05:15:06)

<details>
<summary>Changes</summary>

### Patch Changes

- 82abbf2: Enhance the data update tool to support executing queries independently of custom code logic.
- 3f1966a: Fixed grouping failures due to foreign key constraint violations
</details>

## 6.9.4 (2025-06-30 19:27:16)

<details>
<summary>Changes</summary>

### Patch Changes

- 5015a9a: Update front-end and API to support one-to-many carrier relationships.
- a8c926c: Add bulk receivable calc for policy.
</details>

## 6.9.3 (2025-06-28 01:04:43)

<details>
<summary>Changes</summary>

### Patch Changes

- b690e48: Set policy_year_end to start year when end year is `Not applicable'
</details>

## 6.9.2 (2025-06-27 16:51:32)

<details>
<summary>Changes</summary>

### Patch Changes

- 7c1e7a0: Replace comp reports data source from snapshotdata to accounting transactions part IV
- Updated dependencies [7c1e7a0]
  - common@0.23.8
</details>

## 6.9.1 (2025-06-27 16:03:26)

<details>
<summary>Changes</summary>

### Patch Changes

- ca19604: Added a new display-only Excess field to the Policies page, consistent with fields like Target Premium and Annualized Revenue.
</details>

## 6.9.0 (2025-06-27 04:25:17)

<details>
<summary>Changes</summary>

### Minor Changes

- 65a04c6: Add validation of date range when bulk-updating agent carrier grid levels.
### Patch Changes

- ced82c3: Optimize the Document Profile page:
  1. Add account company selection and display.
  2. Add document sync functionality to sync documents with related companies.
  3. Add sorting functionality to the Document Profile view.
  4. Add pagination to the Document Profile view.
- 05a6ccf: Hotfix the validation issue where clicking ‘Confirm’ causes some fields to be missing.
- 63837a1: Reports now format agency_payout_rates as percentages too.
- deee7cf: Enhanced SO Worker to support configuration of custom API endpoints
</details>

## 6.8.6 (2025-06-26 19:28:31)

<details>
<summary>Changes</summary>

### Patch Changes

- 4fb97e7: Add hierarchy tag for comp report creation if the comp calc method used is not referral. Only for Transglobal account.
- 467ffad: Fix circular dependency issue on Document part
- 1cf7578: Fixed agentCommissionPayout calculation on backend.
- Updated dependencies [cf38844]
- Updated dependencies [1cf7578]
  - common@0.23.7
</details>

## 6.8.5 (2025-06-26 07:54:07)

<details>
<summary>Changes</summary>

### Patch Changes

- 6c97bf9: Enabled report processors for all roles.
- Updated dependencies [6c97bf9]
  - common@0.23.6
</details>

## 6.8.4 (2025-06-26 04:21:21)

<details>
<summary>Changes</summary>

### Patch Changes

- 710bafc: Normalized formatting for commissions amount and rates.
- fd6faf8: Increase payload size limit for data actions preview endpoint
- a76f08e: Suggested change
  Refactored payout rate formatter logic and zero-value filtering for commissions page.
- 7470e72: Delete orphan virtual records after the grouping/deduping process
- 8974884: The generated virtual records will carry over the document_id from the grouped items, and the commission rate will retain two decimal places.
- Updated dependencies [710bafc]
- Updated dependencies [a76f08e]
  - common@0.23.5
</details>

## 6.8.3 (2025-06-25 03:00:44)

<details>
<summary>Changes</summary>

### Patch Changes

- b913331: Fix TWC agent payout rate calc missing data for comp profile sets
- dc0162f: Do not calculate for the uplines of agents whose compensation profile has hierarchy processing set to none.
- 04101ec: Display an alert message in the agent compensation calculation log if both payer and payee rates are null.
- ad51d3c: Improve reconciliation performance
- 89aaadc: Fix agent receivables not showing up on commissions page
- 20f9bf0: Replace comp reports data source from snapshotdata to accounting transactions part III
- 115b0f1: Upgrade the data update tool to handle code execution with Prisma integration and support for Promises.
- 5d06718: Update TWC agent payout rate formula and support multi-carrier comp profiles
- e6584a3: Fixed data update tool for Policies data entities.
- Updated dependencies [89aaadc]
- Updated dependencies [20f9bf0]
  - common@0.23.4
</details>

## 6.8.2 (2025-06-24 08:09:30)

<details>
<summary>Changes</summary>

### Patch Changes

- ecfacd9: Fix comp grid query performance
- 3624939: Fix the issue where commission rate was stored in decimal format instead of percentage
- 8b237e4: Fixed issue where user could not update processors
- d3acfd1: Fix deselect contacts issue for policy filter
- 8990322: Updated contacts -> `patchData` to convert `start_date` and `end_date` to Date objects
  Expanded export field configurations to include new fields.
  Introduced new handlers for uplines, downlines, and saved reports in the contact export functionality.
  Updated tests to cover new handlers and ensure correct data formatting.
- 8669195: Added auto header removal for spreadsheet processors to handle inconsistent mappings.
- 134f822: Added support for select statements based on processing date range when grouping
- 5ed59a1: Sales representatives should pay their uplines when the "Agents' Hierarchy Upline" option is selected in compensation profiles.
- 88f2bf0: Fixed the issue where manual grouping did not respect the useVirtualOption flag.
- fab9042: The Agent Receivable and Override Receivable values should not be multiplied by the split percentage.
- Updated dependencies [d8f52f3]
- Updated dependencies [134f822]
  - common@0.23.3
</details>

## 6.8.1 (2025-06-23 02:31:10)

<details>
<summary>Changes</summary>

### Patch Changes

- 9d270e4: Fixed lint warns
- 88da7c8: Add `Case Manage Pool` to the agents for BGA account
- Updated dependencies [9d270e4]
  - common@0.23.2
</details>

## 6.8.0 (2025-06-21 05:55:46)

<details>
<summary>Changes</summary>

### Minor Changes

- 9c0e9f9: SO integration now syncs policy splits when syncing policies
### Patch Changes

- 216d9c6: Fix the issue where the commission amount's sign does not align with the payout rate's sign.
- de2f9bb: Replace comp reports data source from snapshotdata to accounting transactions part II
- 59de964: Added reconciled_at to statement_data and updated the reconciliation process to fill the reconciled_at field
- 302466b: Implement unselected agents for the dashboard filter
- de2f9bb: Replace comp reports data source from snapshotdata to accounting transactions part I
- 535bb48: Only updates the newly updated carrier data for MAG worker
- 6ff9e59: Allow uploads up to 25mb for data submitted through the data update api.
- 7eab225: Refactor getAgentHierarchy method to accept date range parameters
- Updated dependencies [de2f9bb]
- Updated dependencies [302466b]
  - common@0.23.1
</details>

## 6.7.0 (2025-06-19 20:18:24)

<details>
<summary>Changes</summary>

### Minor Changes

- 370baa2: Implemented BA MOO-specific grouping calculation method
- 827e408: Fix issue that user can't edit agents.
### Patch Changes

- 6e1e7f0: Fix comp profile set not saving with effective date
- Updated dependencies [370baa2]
  - common@0.23.0
</details>

## 6.6.1 (2025-06-19 02:50:07)

<details>
<summary>Changes</summary>

### Patch Changes

- a0a3e8a: Implemented a new services for handling AI provider interactions.
  Added abstraction layer to support multiple providers (OpenAI, Grok, Claude).
  Introduced provider-specific configurations and API clients.
  Updated prompt processing logic to be provider-agnostic.
  Added basic usage examples and documentation.
- 31fe4fa: Add relation field Commission → Commission amount to dashboard for Policies
  Remove scrollbar from widget
  Automatically select appropriate formatter based on the selected field
  Update UI: instead of using Policies.customer_name, display as Policies → Customer name
- 11c1020: Fix the issue where reading undefined data when login
- Updated dependencies [a0a3e8a]
  - common@0.22.1
</details>

## 6.6.0 (2025-06-19 01:20:20)

<details>
<summary>Changes</summary>

### Minor Changes

- 1ec6573: Fix issue users can't bulk update documents
- 2c36d77: RiskTag now can sync member count back to the BenefitPoint
### Patch Changes

- 36bd470: Add flags filter for commissions.
- 949fef0: Move time period filter to the widget builder as the filter is only used for the time bucket
- 62f69fb: Replace comp reports data source from snapshotdata to accounting transactions part I
- 039fc9c: Include report for statement in comp profile matching service.
- Updated dependencies [2c36d77]
  - common@0.22.0
</details>

## 6.5.1 (2025-06-18 23:17:46)

<details>
<summary>Changes</summary>

### Patch Changes

- f75e211: Added new endpoint for per agent accounting transactions management
- 91e6f3c: Align agent_commission_payout_rate between web and export
  Fix per policy scope calc not accounting for previously used commissions (Trawick)
  Fix receivable calc not including company filter when looking up agent level
- bed021d: Fixed issue where users could not update account-specific companies.
</details>

## 6.5.0 (2025-06-18 06:00:21)

<details>
<summary>Changes</summary>

### Minor Changes

- bcf5620: Consolidate comp grid products when syncing with MAG worker
- 3f30a1b: The new grouping process generates virtual records based on the grouped items and infers the correct premium amount and commission rate according to the calculation method specified in the grouping rules.
### Patch Changes

- d2d710e: Fix the issue where the writing agent's split incorrectly shows 100% instead of 0% when the effective split for the downline agent is 0 or NaN.
- 3f30a1b: This change allows clients to set up grouping rules by specifying on how to filter the data and how the grouping keys are transformed for specific rule
- 65521c1: Optimise the Document import trends and Company documents count charts to show "manual" vs "auto" import methods.
- 5f38ce2: Fixed bug where processor could not be removed or saved to company
- b81f801: Fix the issue where the global company cannot be updated.
- Updated dependencies [2aa1fb5]
- Updated dependencies [3f30a1b]
- Updated dependencies [3f30a1b]
  - common@0.21.0
</details>

## 6.4.0 (2025-06-17 07:40:08)

<details>
<summary>Changes</summary>

### Minor Changes

- e54ba48: Add new data tool for bulk updating agent carrier grid levels.
### Patch Changes

- a2f9e5f: Added Trawick to PolicyScope bulk calculate comp allowlist
- 71030fc: Update the data update preview API and frontend to display commission flags and their log.
- f7af2f8: Add POLICY_STATE as geo_state for Transglobal data sync.
- 1147431: Add agency receivable for commission from the policy when the commission's agency receivable is missing or null.
- e7d871c: Add 'All' sheet option for multi-sheet spreadsheets to combine all worksheets with source identification, also apply the logic the related part auto/manual document processing.
- 2cf84e0: Add sync_id field to export table configurations
- c6549fd: Now MyAdvisorGridsWorker supports syncing agent hierarchy and agent level
- 7aac4f8: Associate current agents with the appropriate compensation profile when the "Apply to Downlines" option is selected for an upline agent in the comp profile settings.
- 845fc0f: Comp reports: Cascade soft delete accounting transactions when deleting comp reports
- e01e682: Improve reconciliation data handling and implement version check for export
- 7f5dbaf: Disable selecting startTime > endTime and vice versa in BasicDateRangePicker
Use normalizeFieldValues inside filterFieldValues to avoid duplicate filter values
Select additional report_data_id to display the policy document in reconciliation v1
Fix the where condition to correctly retrieve values for Compensation type v2
When using reconciliation v2, hide the checkboxes to prevent edit and delete actions
</details>

## 6.3.11 (2025-06-13 17:57:14)

<details>
<summary>Changes</summary>

### Patch Changes

- 592a243: Added new field to the "Commissions" page, "Advanced Amount" to track the "Advanced" column in carriers' statements.
- Updated dependencies [592a243]
  - common@0.20.6
</details>

## 6.3.10 (2025-06-13 06:46:55)

<details>
<summary>Changes</summary>

### Patch Changes

- 4eba4f8: Add support for WorldChanger's custom agent payout rate calc.
</details>

## 6.3.9 (2025-06-12 23:32:27)

<details>
<summary>Changes</summary>

### Patch Changes

- bb757e1: Add total amount in the bottom of comp report excel export
- 5607e58: Resolve the issue where the writing agent's split is calculated incorrectly based on the downline agent's data.
- 1d887fd: Report processors - replaced codemirror with fully featured processor playground
- 62fa847: Fix the issue where "data_imports" can't be created due to relational field conflicts, and add "processor_str_id" and "mapping_str_id" fields to "data_imports" during both manual and auto import processes, then display these fields in the data imports view.
- fa3c735: Include the old comp_grid_levels relation when doing the receivable calculation
- Updated dependencies [1d887fd]
  - common@0.20.5
</details>

## 6.3.8 (2025-06-12 06:08:12)

<details>
<summary>Changes</summary>

### Patch Changes

- b076570: Ensure the payout rate is accurately adjusted based on the payee rate of the current agent and the payee rate of their downline agents.
</details>

## 6.3.7 (2025-06-12 05:20:21)

<details>
<summary>Changes</summary>

### Patch Changes

- d6ef171: Add mapping_str_id to data_imports table
- ef4b827: Ensure the payout rate is accurately adjusted based on the payee rate of the current agent and the payee rate of their downline agents.
- db4dd35: Fixed the Issue: In certain scenarios, reconciled statements were not assigned the agent from their corresponding reconciled policy.
- a36e031: Showing the account name instead of the account ID on the Global Companies page, let user easier to identify each account. Also add account filter for Global Companies page.
</details>

## 6.3.6 (2025-06-11 09:39:48)

<details>
<summary>Changes</summary>

### Patch Changes

- 9b0b1ca: Fix issue where clearing 'statement amount' or 'bank amount' caused save to fail. Previously, when users cleared the values (set them from a number to empty), the fields became undefined, which led to validation or parsing issues that prevented document saving.
- c6eb0b3: Fix accounting transaction details not saving
- Updated dependencies [8ee096a]
  - common@0.20.4
</details>

## 6.3.5 (2025-06-10 19:59:47)

<details>
<summary>Changes</summary>

### Patch Changes

- d000002: Undo req.body.role_id strip from middlewares.ts
- 6caef09: Add profit and % profit to reconciliation
- b30fba2: Fix newly created date range not showing.
- Updated dependencies [86b5b44]
  - common@0.20.3
</details>

## 6.3.4 (2025-06-10 09:35:04)

<details>
<summary>Changes</summary>

### Patch Changes

- d920336: Add support for importing Fintary format comp grids
</details>

## 6.3.3 (2025-06-10 05:21:34)

<details>
<summary>Changes</summary>

### Patch Changes

- b2ad387: Auto proxy all created*\* & updated*\* fields, so we don't need to explicitly passing corresponding data.
- c80cd7e: Fixed agent compensation values when exports CSV from comissions page.
- 048751e: Fix for accounting transactions in agent page not showing the total amount correctly.
- Updated dependencies [c80cd7e]
  - common@0.20.2
</details>

## 6.3.2 (2025-06-06 20:42:25)

<details>
<summary>Changes</summary>

### Patch Changes

- e491e88: Add tags field to accounting transaction details in the agents page
- Updated dependencies [e491e88]
  - common@0.20.1
</details>

## 6.3.1 (2025-06-06 18:37:51)

<details>
<summary>Changes</summary>

### Patch Changes

- df2471d: Fixed expected receivable values from commissions when export file
</details>

## 6.3.0 (2025-06-06 16:02:22)

<details>
<summary>Changes</summary>

### Minor Changes

- 0c7e4c0: Added mapping support for e2e document processing. Now users can select a mapping under the document profile within the company page. If the uploaded file is a spreadsheet and no appropriate processor is available, the system will run the selected mapping and automatically import the data provided all conditions are met.
### Patch Changes

- 9e184d5: Enhance both the front-end and back-end of the data update configurations to display data update actions and criteria clearly.
- fd659b0: Add models to prompts table
  Update UI prompt table, form
  Remove the profile_str_id field when creating or updating, as it does not exist in the schema
  Change the model used for VertexAI
- 08030a6: Updated the AgencyIntegratorWorker to use optional chaining when accessing Party data, ensuring that the code handles cases where data may be undefined. This change improves the robustness of the data processing logic.
- 464a508: Add multiplier check for no useCompGrid in comp calc.
- Updated dependencies [0c7e4c0]
  - common@0.20.0
</details>

## 6.2.0 (2025-06-05 20:35:05)

<details>
<summary>Changes</summary>

### Minor Changes

- 48e667f: Customize the welcome email for The World Changers
</details>

## 6.1.6 (2025-06-05 19:50:15)

<details>
<summary>Changes</summary>

### Patch Changes

- e7d1191: Fix the issue where classification couldn’t run during email upload.
</details>

## 6.1.5 (2025-06-05 15:59:31)

<details>
<summary>Changes</summary>

### Patch Changes

- eb060ec: Dynamic select is not called at report page because the header config does not yet include the table field.
- 0733e5f: Fix sorting on the commissions page for commission rate, commission amount, and premium amount
- 7531c58: Fixed sentry error on prisma.data_imports.create
</details>

## 6.1.4 (2025-06-05 05:31:52)

<details>
<summary>Changes</summary>

### Patch Changes

- e3fd2ef: Upgrade nanoid to v5
</details>

## 6.1.3 (2025-06-05 05:10:30)

<details>
<summary>Changes</summary>

### Patch Changes

- a948b3c: Add a “Custom” criteria method for data actions criteria.
- 16c9861: Fix the issue where receivable calc is not working due to the relation change of comp_grids and comp_grid_levels
- 3c9c274: Fix the issue where a global company couldn’t be saved
- 70b9f4e: Add payer grid rates to agent commission calc log.
- 237ef12: When a statement has a period date that falls within the effective range of the policies, or exactly matches a policy’s effective_date, it is always reconciled to the newest policy.
- Updated dependencies [798d6c3]
  - common@0.19.2
</details>

## 6.1.2 (2025-06-04 23:15:56)

<details>
<summary>Changes</summary>

### Patch Changes

- b7f7b25: Add new Timezone decorator to extract timezone from request headers, defaulting to 'UTC'
  Apply timezone when formatting dates in the Accounting Transactions table
  Updated constants to include HTTP header for timezone
- 42a55c8: Add log formatter field in data update actions.
- 08954e9: Stacked bar chart to support commission payout by group by month
- 9a43b2d: Add missing grid levels column to agent exported csv
- Updated dependencies [b7f7b25]
- Updated dependencies [ced8c14]
  - common@0.19.1
</details>

## 6.1.1 (2025-06-04 07:06:42)

<details>
<summary>Changes</summary>

### Patch Changes

- 2bd0b17: Fix bug of comparing the agent's and downline's multipliers to calculate the agent's current multiplier and payout rate.
</details>

## 6.1.0 (2025-06-04 04:02:21)

<details>
<summary>Changes</summary>

### Minor Changes

- c1fdfb5: Implement the new database model for DocumentProfile and its relationships. Updated the related codebase across multiple modules and built a new document profile management view.
  Key changes include:
  1. New DocumentProfile view
  - Allows creating, editing, viewing, and deleting individual document profiles.
  - Supports linking profiles to documents, global companies, mappings, prompts, and various configuration settings.
  - Shows the total document count under each profile and allows accessing special files via links.
  2. Document view updates
  - Enables document selection of an associated DocumentProfile.
  3. Company / Global company view updates
  - Allows associating companies with specific document profiles.
  4. Processor, Processor selector, E2e document processing, Data import, and Mapping page updates
  - Updated relationship logic to support the new DocumentProfile structure.
### Patch Changes

- 721040a: Add support for comma-separated values in CONTAINS and NCONTAINS operators in field matcher when 'Treat as array' option is true.
- e9350e1: Fixed bug where users could not save global processors (which are associated with a different account) in the companies page
- 9693592: Added reverse comp report approval script to code base
- 227f611: Resolve merge conflicts in the code
- Updated dependencies [721040a]
- Updated dependencies [c1fdfb5]
- Updated dependencies [9693592]
  - common@0.19.0
</details>

## 6.0.1 (2025-06-03 07:15:38)

<details>
<summary>Changes</summary>

### Patch Changes

- 272e002: Add validation results table for flagging system.
</details>

## 6.0.0 (2025-06-03 06:52:24)

<details>
<summary>Changes</summary>

### Major Changes
- 5d5b1f4: Add sync_id to Gmail Sync Documents
### Patch Changes

- 55f50c6: Compare the agent's and downline's multipliers to calculate the agent's current multiplier and payout rate in comp calc.
- ebea4bb: Enhancement: Validate integration variables against environment variables
- f382556: Add referral tags to transglobal comp report only if calc method is referral
- d92cf5c: When saving snapshot_data into saved_report, retrieve all fields from the data source to support view changes in the "Views and Fields" settings.
  Display the fields in the report based on the configuration defined in the settings.
- 8b66187: Add support for setting json fields (like agent_commissions) in data actions tool
- 9fa3563: Migrated firebase-admin.js to typescript
- 8cadb38: Capture only unknown exceptions and send them to Sentry
- b021d94: Fix currency format for premium amount and agent commission data in excel report file
- 52bcefe: Fetching comp grid level when comp_grid_id is null in comp calc.
- 2e487a8: Migrated deprecated Formatter to Typescript
- a6e36b3: Ensure that the payee and payer compensation grids are filtered correctly.
  Ensure comp profile matcher running correctly.
- 2da396a: Add 'expected_result' field to keysToRemove in the getSelectStatement function, this field does not actually exist in the database.
- 140026b: 🚀 Allow users to enable data syncing feature by adding integration configurations
- 62ff32e: Migrated csv-loader.js to typescript
- 958a2fa: Fix ReceivableCalcService test
- Updated dependencies [d92cf5c]
- Updated dependencies [2e487a8]
- Updated dependencies [064f6c6]
  - common@0.18.4
</details>

## 5.13.3 (2025-05-29 18:53:31)

<details>
<summary>Changes</summary>

### Patch Changes

- d291b3a: Introduced integration tests covering companies, comp grids, users, and reports (more to come)
- 2038c82: Added referral tag to comp report data when calc method is referral for transglobal
- Updated dependencies [a8f9d9e]
  - common@0.18.3
</details>

## 5.13.2 (2025-05-29 10:07:22)

<details>
<summary>Changes</summary>

### Patch Changes

- 00d3fcc: Replace Promise.all with limitConcurrency in comp grid imports
- 00d3fcc: Enable search for Company selector in Agent arrier/agency levels
- 6ff4e82: Fix the issue where agents syncing will fail when no crm agents are returned
- e1cf732: Addressing the issue of multi-carrier comp profiles not retrieving the correct grid levels.
- c4397fa: When running bonus calculations, take the split_percentage into account
- 00d3fcc: Reduce web app update notification from 12 hrs to 4 hrs after an update is available
</details>

## 5.13.1 (2025-05-29 05:39:24)

<details>
<summary>Changes</summary>

### Patch Changes

- 370f0c9: Enhance the compensation profile matching logic to support multi-carrier compensation profiles.
- Updated dependencies [0044f7f]
  - common@0.18.2
</details>

## 5.13.0 (2025-05-29 04:41:56)

<details>
<summary>Changes</summary>

### Minor Changes

- 35d57af: Replaced the 'Expected Receivables' column with 'Agency Receivable', 'Agent Receivable', and 'Override Receivable' columns for commissions and policies.
### Patch Changes

- 45c3b73: Replace all calls from Promise.all to limitConcurrency in api/contact.
- 8ed0dde: Add additional metrics:
  • Pie chart for document upload sources (Web, API, Email)
  • Pie chart for document import methods (Manual, Auto)
  • Pie chart for E2E document processing statuses (Processing, Pending Review, Processed - Auto)
  • Line chart for E2E document imports over time
- e044dbf: Display the agent name with the amount in agent commisssions when agent settings are configured to show agent downline commissions.
  Updated logic for calculating report amounts to prioritize specific agent commissions based on contact ID.
- e8c9141: Fixed error when updating data extractions without id
- c64d80f: Refactor /admin/documents API to replace Promise.all with limitConcurrency for improved concurrency control and error handling.
</details>

## 5.12.3 (2025-05-28 07:46:41)

<details>
<summary>Changes</summary>

### Patch Changes

- 95948bd: Fix Last Uploaded Time Value and Update Logic to Retrieve All Attachments
- bf55b98: Update compensation calculation logic to handle profiles with hierarchy processing set to 'none'.
- 508ba59: Fix ungroup issue caused by build issue of losing original file path info
- cf5ab09: Refactor contact api to use proper DI
- a1090ba: Support matching all for multi-carrier comp profiles without specific carriers in comp calc.
</details>

## 5.12.2 (2025-05-27 08:44:04)

<details>
<summary>Changes</summary>

### Patch Changes

- c37b6c7: Fix splits syncing failure caused by not coercing policy number to string
- 51dc80f: Display Multiplier information in Agent commission log when exporting
- 9064362: Add Agent commissions (Agents only) Agent commissions (Sales reps only) for data field
- 3009365: Add data filtering for producers in the data sources: Customers, Agents, and Transaction Details
- Updated dependencies [51dc80f]
  - common@0.18.1
</details>

## 5.12.1 (2025-05-27 06:47:44)

<details>
<summary>Changes</summary>

### Patch Changes

- 575a017: Fix popoulating missing agents
</details>

## 5.12.0 (2025-05-27 02:51:45)

<details>
<summary>Changes</summary>

### Minor Changes

- d0b9459: Enable syncing agent splits for AgencyIntegratorWorker & syncing & linking all agents for policies
### Patch Changes

- 99a8bbd: Fetch correct comp grid for multi-carrier comp profile for comp calc
- 00ffb53: Removed the task locking to allow concurrent document processing for the same account.
- 7c2700c: Fix export expected receivables values in commissions
- 384209d: Added agents agency levels and carrier levels
</details>

## 5.11.6 (2025-05-26 22:54:22)

<details>
<summary>Changes</summary>

### Patch Changes

- dce7789: Fix date filter at document page not working properly and show error when end date is before start date
- ee56c3f: limitConcurrency ensures that results are returned in the order of the input.
</details>

## 5.11.5 (2025-05-26 22:33:37)

<details>
<summary>Changes</summary>

### Patch Changes

- 8e0e25a: Add more transfer methods for document classification to solve the file from different structure (Buffer, string, base64url, arrayBuffer).
- d57ee38: Fix: target premium value is not formatted as currency in reconciliation and policy export file
</details>

## 5.11.4 (2025-05-26 08:04:52)

<details>
<summary>Changes</summary>

### Patch Changes

- c526b66: Fixed an issue of group name filter that doesn't work properly at commissions and policies page
- f511da4: Add the custom_view_name field to Agent settings
  Include the id to support updating Agent settings
  Get the correct current account_role_setting when updating
- 8468e71: Fix the document upload api failure caused by service binding
- 67dd206: Changed multiline-text input to codemirror for report processors. Added a few rendering UI fixes as well for the report processor views.
- 19f79a6: Filter the testing fields 'receivable_value_agency_rate', 'receivable_value_agent_rate', 'receivable_value_override_rate' to solve the commission page can't be loaded issue.
- 83bac27: Fix for comp grid rates not saving on comp grid viewer for new ordered grid levels
- Updated dependencies [c526b66]
- Updated dependencies [f511da4]
- Updated dependencies [26d347a]
- Updated dependencies [67dd206]
  - common@0.18.0
</details>

## 5.11.3 (2025-05-23 05:24:23)

<details>
<summary>Changes</summary>

### Patch Changes

- 4bdaf92: Update "DocumentProcessing" worker to use existing data processing record through "dataProcessingService" instead of creating new record, fixing the issue of missing account information in processing logs.
- f2c11c3: Fix: Corrected Prisma relation orderBy for aggregate sorting
- ec09247: Comp reports fix for agents not showing up and filtering out sales reps
- e4b9ae8: Add option for include grouped commissions in the comp reports
- e8a2cbd: Fix the issue where not able to create worker tasks caused by circular deps
</details>

## 5.11.2 (2025-05-22 07:15:46)

<details>
<summary>Changes</summary>

### Patch Changes

- 9cd0c0b: Add support for company, document type, and statement classification in email and API document uploads. If conditions are met, include them in the auto document processing flow. Also, add a verification system for this field.
- 49d0be7: Update api/data_processing/grouping to extend the base handler for improved error logging and enhanced debugging capabilities.
- 01ba096: Add more loggings to cloud task & queue service
- 4d53798: Migrate all dto to Zod schema
- fdd3f25: Fix the issue that some files may fail to be extracted by ExtractTable in e2e document processing when uploading a large number of files.
- 149477d: Added exception handling and tests for CloudTaskService
- fb2ff53: Fix result formatter default selection
  Add Sum (Accumulated), Count (Accumulated) for data field filter
- adca466: Remove aggregation method from the widget builder
  Clean up result formatter from widget builder
  Move button to right for widget builder
- Updated dependencies [9cd0c0b]
- Updated dependencies [4d53798]
  - common@0.17.3
</details>

## 5.11.1 (2025-05-21 06:56:06)

<details>
<summary>Changes</summary>

### Patch Changes

- 21483f2: Fixed error when run Data Action tool with commission rate based on custom rule
- c2c85c7: Added support for displaying rates in the comp grid viewer based on ordered comp grid levels. This feature respects agent settings (ExtendedDownlineDataAccess.compGridRates) and applies to all accounts.
- 90662d0: Allow users to update synced agents
- adf3323: Return compensation type = Renew if policy start year is empty or gte 1
- 61f72aa: Add a new pull request review report task
- 5e709fe: Fixed login issue where new users would result in errors.
- 593cb0e: Remove redundant id assignment logic in update method
- 893a186: Remove result formatter from widget builder and add default any in the data field selector
- d00b60f: Integration between ui and api for comp profile matcher tools.
- 51c4e1c: Resolve bugs in update and add functionalities for multi-carrier mode compensation profiles.
- f308e6b: Update last_uploaded_time to the latest date among the content emails
- 9c68ba6: Data actions tool: Extended the within operator to handle dates within a range that starts before or after the reference date
</details>

## 5.11.0 (2025-05-19 07:04:29)

<details>
<summary>Changes</summary>

### Minor Changes

- 7952175: Implemented global ordered comp grid levels
### Patch Changes

- a831d27: Add comp profile matcher api
</details>

## 5.10.3 (2025-05-19 05:59:23)

<details>
<summary>Changes</summary>

### Patch Changes

- 727609f: Remove reporting of invalid account identifier errors to Sentry.
- 326ba40: Enhance commission calculation to support retrieving applicable compensation profiles for both single-carrier and multi-carrier modes.
- eea9234: Aligned all `ESLint` versions across the workspace and moved the base configuration to the root project.
- 819e6db: Add a 'Blank' option to the carrier select dropdown in the comp profile view.
- Updated dependencies [eea9234]
  - common@0.17.2
</details>

## 5.10.2 (2025-05-19 03:33:36)

<details>
<summary>Changes</summary>

### Patch Changes

- cb5b9fc: Fix and improve logging in Gmail sync / email file uploads
- 24d046d: Improve policy and commissions database indexes to improve filter and filter option performance
- 442592b: Only show "Show duplicates" option for Fintary admins
- fabeb4b: Dashboard: Fix producer selection, enable search, refactor/clean up ui
- Updated dependencies [442592b]
  - common@0.17.1
</details>

## 5.10.2 (2025-05-17 00:10:24)

<details>
<summary>Changes</summary>

### Patch Changes

- ced79bc: Set correct NODE_ENV for different environments
</details>

## 5.10.1 (2025-05-16 03:44:33)

<details>
<summary>Changes</summary>

### Patch Changes

- 2d1b73d: Add all entities into dashboard
Policies
Commissions
Agents
Agent payouts
Customers
</details>

## 5.10.0 (2025-05-16 02:02:45)

<details>
<summary>Changes</summary>

### Minor Changes

- f4256c2: Added report processor capability for custom policies and commissions exports.
### Patch Changes

- b26d504: Fix data actions criteria for payment_date
- Updated dependencies [b112990]
- Updated dependencies [f4256c2]
  - common@0.17.0
</details>

## 5.9.9 (2025-05-15 21:02:38)

<details>
<summary>Changes</summary>

### Patch Changes

- 90805d1: Optimise BA agents syncing and the logic of linking agents to policies
- be6c467: Update DMI integration mappings, agents linking and support incremental policy syncing
- 56914d1: Simplify the widget builder filter, merge group by filter and data field filter together.
- 397777f: Create individual endpoints for /account/settings/[[...params]] file to fix unauthorized error in prod
- 2395044: Update "Pay a set rate" calc method to use calculationBasisAmountSplitted as basis. Limited to BrokersAlliance for now.
- aa6a425: Log error stack into gcp logs
</details>

## 5.9.8 (2025-05-14 09:05:26)

<details>
<summary>Changes</summary>

### Patch Changes

- 6776e5a: Support comp calc in accounts with > 32k agents
</details>

## 5.9.7 (2025-05-14 03:51:00)

<details>
<summary>Changes</summary>

### Patch Changes

- b3f555e: Optimise tracing & logging
- af6d8ee: Add debug loggings
- Updated dependencies [4a1ec00]
  - common@0.16.0
</details>

## 5.9.6 (2025-05-14 01:52:06)

<details>
<summary>Changes</summary>

### Patch Changes

- Updated dependencies [b7d5bca]
  - common@0.15.2
</details>

## 5.9.5 (2025-05-14 01:45:52)

<details>
<summary>Changes</summary>

### Patch Changes

- 7cb1280: Resolve limitation of 32k agents in dynamic-select endpoint
</details>

## 5.9.4 (2025-05-13 21:08:17)

<details>
<summary>Changes</summary>

### Patch Changes

- 5c8b588: Fix issue where empty comp profile sets could not be created (without comp profiles specified)
- 3d471f8: Use LoadingButton for BasicDialog, fix LoadingButton colors for disabled and loading states, fix creating comp profiles without carriers
- 61456e7: Fix an issue where individual exports in the compensation report were missing most of the expected files.
- a5a0f0b: Add account_id column to company_mappings table
</details>

## 5.9.3 (2025-05-13 18:51:25)

<details>
<summary>Changes</summary>

### Patch Changes

- 1091dde: Add a field to select custom_view_name for the agent and handle its display accordingly.
- 56dd937: Update account_role_settings to require custom_view_name and adjust related functionality
  Able to create new account_role_settings in the Views and Fields screen
- f9ff5ab: Fix custom view name null values
- 5311e4f: Update the compensation profile view and form to support both single-carrier and multi-carrier modes.
- b82b615: Link comp grid products with company_products to enable full functionality
- 357b4c5: Fix commissions page not loading for data specialist role
- 96483a5: Add E2E document processing with log and improve filtering by "processors" and "import status" in the workflow.
- 2bdf235: Fix an issue where producers could view policies belonging to other producers.
- 9711f2a: Remove the account_role_settings field when updating contacts
- Updated dependencies [56dd937]
  - common@0.15.1
</details>

## 5.9.2 (2025-05-12 07:33:01)

<details>
<summary>Changes</summary>

### Patch Changes

- 9b4c1fd: Fix: allow synced data update after integration is disabled
- d1c05cb: Fix bug related to adding commission profile sets
- 1a03924: Consider policy_start_year when inferring compensation type and default to "Renew" if over 1 year
</details>

## 5.9.1 (2025-05-12 02:01:54)

<details>
<summary>Changes</summary>

### Patch Changes

- 7187b89: Fix reconciliation agent data export
</details>

## 5.9.0 (2025-05-12 00:30:02)

<details>
<summary>Changes</summary>

### Minor Changes

- 8f500a3: Added new setting to hide commissions with payout calculated
- 31e8ca9: Agent settings, allow upline to view downlines commissions and policies based on hierachy and based on grid payout levels
### Patch Changes

- dd287d3: Add support for bar chart and line chart for accumulated value
- 5b3177a: Fix issue with BA compensation grid syncing where product names were not populated correctly.
- 6cb4512: Add a new option that will indicate no date filter to be applied for the widget
- ebfed41: Producer comp grid levels configuration, don't show deleted agent grid levels in comp grid viewer
- Updated dependencies [8576995]
  - common@0.15.0
</details>

## 5.8.1 (2025-05-08 16:58:20)

<details>
<summary>Changes</summary>

### Patch Changes

- d0be894: Fix the issue where the AgencyIntegrator fails to sync entities for large dataset accounts due to excessive data being queried in a single request
</details>

## 5.8.0 (2025-05-08 07:54:09)

<details>
<summary>Changes</summary>

### Minor Changes

- 5537b78: Add new filters to commissions and policies: transaction type and group name
### Patch Changes

- aa534a6: Add support for premium basis in policy scope calc.
- 3936393: Fix saving widget layout and widget creator button overlap
- 630335f: Add payer_grid_level_name, payee_grid_level_name, and single_carrier_mode to the agent_commission_schedule_profiles table
- 5ed9a3a: Add an "upload_source" field and label to differentiate upload sources: Web, API, and Email.
- Updated dependencies [cc856f2]
- Updated dependencies [5ed9a3a]
  - common@0.14.9
</details>

## 5.7.0 (2025-05-07 18:07:03)

<details>
<summary>Changes</summary>

### Minor Changes

- f5a02e5: AgencyIntegrator now syncs products via the plan search endpoint
### Patch Changes

- 9cb772e: Fixes exception handling for when the incoming auth idToken is malformed or missing.
</details>

## 5.6.3 (2025-05-07 16:31:29)

<details>
<summary>Changes</summary>

### Patch Changes

- 5bca96d: Enhanced commission calculation logging by including `Scope` and `policyCommissionsUse` details when calc method is `policyScopeCalcsNew`.
- 2f035ad: Fix the issue where "agent_commission" cannot be imported when the account admin is also an agent.
- 76381e7: Fixed a bug causing `Cannot read properties of undefined (reading 'effectiveDate')`. This occurred when policies.value[0] was undefined due to an empty result from getPolicyDetailList.
</details>

## 5.6.2 (2025-05-07 05:49:44)

<details>
<summary>Changes</summary>

### Patch Changes

- ee412cc: Default to `ACH` if payment_type is empty string in in BenefitPoint statement sync
</details>

## 5.6.1 (2025-05-07 04:42:53)

<details>
<summary>Changes</summary>

### Patch Changes

- 519889d: Add custom_view_name to account_role_settings table
- ad25c3b: Fix bug where default pages were not visible under "View" and "Fields" for non-admin roles
</details>

## 5.6.0 (2025-05-06 19:04:25)

<details>
<summary>Changes</summary>

### Minor Changes

- cb28d7d: Add processing date and deposit date to filter of documents
### Patch Changes

- ea7f5ea: Update the classification prediction results to save the original output, including those with confidence scores below 0.9 and type results (for model training purposes).
- ba5a548: Implemented resend user invite feature
- 37c5975: Implemented new date operators (Within, before, after, n days/months/years) for the Data Actions tool.
- Updated dependencies [9be6b1d]
- Updated dependencies [37c5975]
  - common@0.14.8
</details>

## 5.5.11 (2025-05-05 08:33:39)

<details>
<summary>Changes</summary>

### Patch Changes

- d84ce2e: Fix the issue that when processor update the import statuses will change to 'none'
</details>

## 5.5.10 (2025-05-05 07:59:52)

<details>
<summary>Changes</summary>

### Patch Changes

- a7a26e5: Adjust statementDataTake correctly
  Optimize the performance of the getReconciliationData function
- c00c194: Tweak the document processing workflow and resolve the issue where ExtractTable cannot be run during the workflow. Also fix the "split_percentage" validation issue. Implement an "process_method" field to distinguish between automatic and manual processing.
- bdfa82b: Add the group by date, company for Agent Payouts
  Add new Data sources - Contacts: able group by name, data fields: Agents balance
  Fix to display only the contact name, excluding the ID
  Replace AccountingTransactionDetailsService with AccountingTransactionsService
- a18a4fc: Set the type of accounting_transactions to "comp_report" when generating comp payout reports
- Updated dependencies [c00c194]
- Updated dependencies [a18a4fc]
  - common@0.14.7
</details>

## 5.5.9 (2025-05-01 19:12:55)

<details>
<summary>Changes</summary>

### Patch Changes

- Updated dependencies [724886d]
  - common@0.14.6
</details>

## 5.5.8 (2025-05-01 17:57:34)

<details>
<summary>Changes</summary>

### Patch Changes

- 8629666: Fix for commissions agents field not being exported correctly in comp reports
</details>

## 5.5.7 (2025-05-01 17:10:44)

<details>
<summary>Changes</summary>

### Patch Changes

- 922760a: Update BA/MAG sync to populate compensation_type from product name/sub
</details>

## 5.5.6 (2025-05-01 04:22:55)

<details>
<summary>Changes</summary>

### Patch Changes

- c6b4813: Move shared widget definition to access column
- dea47e4: Resolved mismatch between total agent commissions in comp reports and commissions page.
- 9a4e222: When syncing comp grid products using MAG integration, use the product name that synced from AI integration instead
- c6b4813: DB change for widgets table to migrate shared column to access column
- 6bae1d1: Fixed company create bug that prevented users from creating new companies.
</details>

## 5.5.5 (2025-04-30 16:49:38)

<details>
<summary>Changes</summary>

### Patch Changes

- 7cbee46: Fix error handling in the user manager page.
- Updated dependencies [e92548d]
  - common@0.14.5
</details>

## 5.5.4 (2025-04-30 04:36:41)

<details>
<summary>Changes</summary>

### Patch Changes

- 7f20a58: Fix account validation error & passing account_id correctly
- fed97e3: In comp report generation, return list of payees instead of writing agents
- Updated dependencies [7f20a58]
  - common@0.14.4
</details>

## 5.5.3 (2025-04-30 00:02:34)

<details>
<summary>Changes</summary>

### Patch Changes

- Updated dependencies [251eaaa]
  - common@0.14.3
</details>

## 5.5.2 (2025-04-29 06:50:26)

<details>
<summary>Changes</summary>

### Patch Changes

- 5526321: Include statement_data.agent_commissions_status in Payout status filter
</details>

## 5.5.1 (2025-04-29 05:33:57)

<details>
<summary>Changes</summary>

### Patch Changes

- 2f23b8e: Allow users to specify payment method when uploading documents and will set to corresponding payment_method when syncing to BenefitPoint
- d2a30ef: Fix user onboarding for error when creating a new user and account, the onboarding flow cannot be completed.
- Updated dependencies [2f23b8e]
  - common@0.14.2
</details>

## 5.5.0 (2025-04-29 02:41:04)

<details>
<summary>Changes</summary>

### Minor Changes

- 6d21eb5: Pay to first active upline agent if current agent is in archived state
</details>

## 5.4.5 (2025-04-28 08:35:03)

<details>
<summary>Changes</summary>

### Patch Changes

- 3ee249e: AI correctly populate the company_product_id
</details>

## 5.4.4 (2025-04-28 07:12:49)

<details>
<summary>Changes</summary>

### Patch Changes

- c1e0e0d: DB change for widgets table to migrate shared column to access column
</details>

## 5.4.3 (2025-04-27 10:12:22)

<details>
<summary>Changes</summary>

### Patch Changes

- 1632bba: Fix document edit failure caused by not correctly connecting account
</details>

## 5.4.2 (2025-04-26 02:13:03)

<details>
<summary>Changes</summary>

### Patch Changes

- 4aafb01: Fix undefined error where Gen3 syncing returns undefined data
- 1951576: Fix export agents function to export all agents
- 6a64202: Fix data fetching issue of regression test
- Updated dependencies [99762a7]
  - common@0.14.1
</details>

## 5.4.1 (2025-04-25 18:20:00)

<details>
<summary>Changes</summary>

### Patch Changes

- ae86be4: Fix deleting and inviting again an already invited user bug
</details>

## 5.4.0 (2025-04-25 16:24:04)

<details>
<summary>Changes</summary>

### Minor Changes

- f5ccea9: Add regression test feature in admin tools to validate new commission calc updates on provided use cases
### Patch Changes

- ba1f4cd: Filter agent options based on data from selected date ranges when creating comp reports.
- 2debfc4: Fix empty comp calc logs being added if not specified
- 2190186: Refresh package-lock.json
- Updated dependencies [f5ccea9]
  - common@0.14.0
</details>

## 5.3.1 (2025-04-24 17:48:54)

<details>
<summary>Changes</summary>

### Patch Changes

- 10185e6: Clean up 'accounts' get endpoint
- Updated dependencies [3e85e99]
  - common@0.13.1
</details>

## 5.3.0 (2025-04-24 06:40:32)

<details>
<summary>Changes</summary>

### Minor Changes

- a2664b5: Fix export data processing stats viewing
### Patch Changes

- d86f430: Add company mappings table
- 58a723a: Added state injection false when inviting new users
- fa0c936: Refactor comp calc by adding all comp calc logics into a seperate modules and add more comp calc unit tests
- 2fbcea0: Update view & fields behaviour for no setting
- 23a2303: Fix bug of activity log export
- dae5c3c: Apply filters to the preview widget to make the previewed widget and generated widget consistent
- f22c1a1: Remove account related field from client data to prevent user from impersonate others
- f01567a: Invited users onboarding workflow asking new users to edit account info fix.
</details>

## 5.2.1 (2025-04-23 05:06:22)

<details>
<summary>Changes</summary>

### Patch Changes

- 945d223: Sync carrier rate to total rate & fix comp grid product display issue caused by no product name
- d759036: Optimize the export process for report_data and statement_data.
- 50115bb: Fixes export issue for accounting_transaction_details
</details>

## 5.2.0 (2025-04-22 20:47:48)

<details>
<summary>Changes</summary>

### Minor Changes

- 978d9b1: Improve performace of table and edit form, use server pagination for dropdown select
- 43c7b76: Allow to input statement month for documents
</details>

## 5.1.1 (2025-04-22 07:14:28)

<details>
<summary>Changes</summary>

### Patch Changes

- 857ba2d: Add a new Source to Dashboard: accounting_transaction_details
  Add a company_id and company relation
- dac2c98: Replace the stringify stats log by normal object.
Limit the sheet name to a maximum of 31 characters.
</details>

## 5.1.0 (2025-04-22 05:40:44)

<details>
<summary>Changes</summary>

### Minor Changes

- c06c609: Extend logic for function alignSign to take the sign of rate
### Patch Changes

- 6cf78ad: Fix build issue
- 728a312: Fix the issue of sso logged user cannot view Reconciliation / Commissions / Policy data
</details>

## 5.0.0 (2025-04-21 21:17:05)

<details>
<summary>Changes</summary>

### Major Changes
- e49e5ac: Add statement_month to documents table
### Minor Changes

- 8b8b5b8: Add commissionUtils class to separate commission utils logic from commissionProcessor class
### Patch Changes

- 6ab2b77: Sync rates for product without returning carrier_product_criteria data
- 905cf8a: Fix Nowcerts post-processing failure caused by not specifying report_data state filter
- 94488e3: Fix comp grid criteria syncing failure caused by not find comp grid product
- cf09c82: - Make sure notes and agent code are strings when syncing agents using SmartOffice worker
- bf1607c: Optimize contact information retrieval in commission report generation
</details>

## 4.8.5 (2025-04-17 20:10:07)

<details>
<summary>Changes</summary>

### Patch Changes

- 847499d: Fix for 0 value in split percentage when editing an agent upline
</details>

## 4.8.4 (2025-04-17 19:57:08)

<details>
<summary>Changes</summary>

### Patch Changes

- b79cbf1: Update sso to support account admin login
- 3909884: Add scripts to build api image locally
</details>

## 4.8.3 (2025-04-17 18:56:26)

<details>
<summary>Changes</summary>

### Patch Changes

- 4b3826a: Add bulk edit to documents page
- ae27a2b: Update COMMISSION_STATUS_EDITABLE in commissionProcessor
</details>

## 4.8.2 (2025-04-16 19:13:34)

<details>
<summary>Changes</summary>

### Patch Changes

- 9f68a9c: Fixed policies agent payout rate override times 100 issues
</details>

## 4.8.1 (2025-04-16 08:46:24)

<details>
<summary>Changes</summary>

### Patch Changes

- a268f04: Fix Agent comp reports PDF exports resulting in "Error exporting data"
- 0143a6d: Update @react-pdf/renderer to 4.3.0
- c50236d: Add support for multiple actions per criteria meet in data actions tool
- 72a92c0: Filter records where related data does not exist
- 9c90f17: Update AgencyIntegorator carrier whitelist for Brokers Alliance
</details>

## 4.8.0 (2025-04-16 04:33:18)

<details>
<summary>Changes</summary>

### Minor Changes

- da7210c: Refactor agent.ts and separate all commission process logic to commissionProcessor.ts
- 5f801b7: Add sorting feature for widget builder to be able to sort and select N items based on field
### Patch Changes

- d474f79: Update compGrid calc method to use downlineRate instead of previousRate
</details>

## 4.7.1 (2025-04-15 19:29:17)

<details>
<summary>Changes</summary>

### Patch Changes

- afab314: Move gmail sync from cron job to cloud scheduler
- a889427: bump version
</details>

## 4.7.0 (2025-04-15 06:51:42)

<details>
<summary>Changes</summary>

### Minor Changes

- 120df69: Added agent payout rate calculation when editing related commission fields values
- 74127bc: Refactor logic of fetch profiles for agents that are not in hiearchy, only fetch profiles for those agents once
- a3d62ae: MAG worker now supports syncing comp grid data
### Patch Changes

- Updated dependencies [120df69]
  - common@0.13.0
</details>

## 4.6.0 (2025-04-14 08:34:07)

<details>
<summary>Changes</summary>

### Minor Changes

- 92e7566: Add global indicator for shared widget
  Update tooltips
  Update the widget setting list to show shared widget with account specific widget
- 752b383: Fixed the issue where retrying a computation after an error led to duplicate computations.
### Patch Changes

- cc0bc20: Fix issue of fetching commissions and add/edit commissions
- Updated dependencies [2b965fe]
  - common@0.12.0
</details>

## 4.5.0 (2025-04-12 00:25:16)

<details>
<summary>Changes</summary>

### Minor Changes

- 887b478: Skip manually reconciled statements in reconciliation process and show Manual reconciler for manually reconciled data
### Patch Changes

- d843e1d: Include account shortname in email to upload documents.
  Make query chips for document states of Pending upload and Pending review Fintary admin only.
  For end users, show documents in Pending review state as Processing.
  Update uploaded at and imported at to be local time instead of UTC.
- b8e51db: Added clear field operation for data actions tool
- Updated dependencies [fd2bc0d]
- Updated dependencies [b8e51db]
  - common@0.11.0
</details>

## 4.4.1 (2025-04-11 05:51:30)

<details>
<summary>Changes</summary>

### Patch Changes

- d6298df: Update the document import process to allow importing the "agent_commissions", "agent_payout_rate", "agent_commission_payout_rate" and "agent_commission_status" fields. ("agent_commission_payout_rate" and "agent_commission_status" have ambiguous type issues, so please consult with the engineering team before using them.)
  Also, add normalization for them to ensure the values are either "currency" or "percentages". Additionally, implement a checker to verify that the key belongs to the corresponding "agent ID".
- a53b76d: Fixed the issue in production that the widget grouped by Agents will result concurrent database calls, which throws exception
</details>

## 4.4.0 (2025-04-11 04:50:35)

<details>
<summary>Changes</summary>

### Minor Changes

- 16e9069: Fix the show linked commission when query filter including id
- 366a5cb: When agent payout rate override is specified and the agents are not in the hierarchy of the writing agent of the statement, extra profiles of these agents will be fetch for further calculation
</details>

## 4.3.1 (2025-04-10 23:08:02)

<details>
<summary>Changes</summary>

### Patch Changes

- e8068b9: Fix duplicate applications of multiplier in comp calc
- 1bb9275: Improve comp grid load time and remove duplicate date ranges (take newest record)
</details>

## 4.3.0 (2025-04-10 17:36:25)

<details>
<summary>Changes</summary>

### Minor Changes

- 9c93924: Add flags column for commissions in ui
- d01793d: Allow users to ungroup grouped commission line items
### Patch Changes

- f66031b: Normalize agent_payout_rate and agent_commission_payout_rate data
- e60b88f: Improve widget x-axis labels to be more likely to show the whole label.
- e5bff8b: Fix the issue on the processor page where the document field only links to documents within the current account, rather than globally. Now it can display all related documents.
- Updated dependencies [f66031b]
- Updated dependencies [18faa4f]
- Updated dependencies [d01793d]
  - common@0.10.0
</details>

## 4.2.11 (2025-04-10 03:17:48)

<details>
<summary>Changes</summary>

### Patch Changes

- 7080ead: Remove "alignSign" application to custom methods Fixed Override and OverrideBonus, allowing negative rates to generate negative values.
- 628ce91: Add flag column to report_data and statement_data table
- c014b8c: Add Policy relations to Data fields
</details>

## 4.2.10 (2025-04-09 19:22:36)

<details>
<summary>Changes</summary>

### Patch Changes

- c36e9e3: Add commission group alerts for commission that linked with others
</details>

## 4.2.9 (2025-04-09 16:55:43)

<details>
<summary>Changes</summary>

### Patch Changes

- cbc3d12: Correct the file name from the Gmail response
</details>

## 4.2.8 (2025-04-09 16:32:52)

<details>
<summary>Changes</summary>

### Patch Changes

- 5c69112: Update export xlsx column width
  Add nested report field when exporting comp report
- 99d883f: Update the donut chart formatter, fixed the decimal point format to be 2 digits
</details>

## 4.2.7 (2025-04-09 05:04:14)

<details>
<summary>Changes</summary>

### Patch Changes

- 2c1056c: Report status approval fix for reports created/approved before new approval workflow
</details>

## 4.2.6 (2025-04-09 04:43:48)

<details>
<summary>Changes</summary>

### Patch Changes

- 1f93f5d: Replace the implicit '\_companies_processors' table with an intermediate table, and add an 'import_status' field to it. And update both the UI for companies and the admin companies UI to allow users to select the 'import_status'.
- Updated dependencies [1f93f5d]
  - common@0.9.3
</details>

## 4.2.5 (2025-04-09 02:07:14)

<details>
<summary>Changes</summary>

### Patch Changes

- 86ee2ec: In document exports, use formatted date, simplify override filename, and add statement_amount and notes fields.
- 10d451a: Updated the document processing page workflow
</details>

## 4.2.4 (2025-04-08 16:39:43)

<details>
<summary>Changes</summary>

### Patch Changes

- 7bce7ff: Add tracking for how long widgets / dashboards take to load
- 3405452: Optimize the feedback of syncing statements error for RiskTag, including showing statement IDs grouped by company, storing alerts and correctly saving stats.
- 8155963: Missing support for uploading some file extensions
- 6b07603: Two decimals formatter fix for BuddyIns account for agent payout rate field.
- Updated dependencies [6b07603]
  - common@0.9.2
</details>

## 4.2.3 (2025-04-07 19:45:20)

<details>
<summary>Changes</summary>

### Patch Changes

- e1e4676: Fix exports failing when resolving too much relational data
</details>

## 4.2.2 (2025-04-07 04:03:59)

<details>
<summary>Changes</summary>

### Patch Changes

- 54593fc: Ignore 404 error when syncing statement back to BeneiftPoint service
</details>

## 4.2.1 (2025-04-05 01:26:02)

<details>
<summary>Changes</summary>

### Patch Changes

- 6274be7: Set target premium and annual revenue per BrokerAlliance's requirements for their AgencyIntegrator integration.
- 12293e7: Use localized Loader for widget preview instead of global (which blocks whole page)
- Updated dependencies [12293e7]
  - common@0.9.1
</details>

## 4.2.0 (2025-04-05 01:05:09)

<details>
<summary>Changes</summary>

### Minor Changes

- b67f6b1: Remove dynamically generated agent_commission_payout_rate and agent_payout_rate columns and surface underlying fields (allows editing values).
</details>

## 4.1.3 (2025-04-04 23:21:34)

<details>
<summary>Changes</summary>

### Patch Changes

- 739fc03: Fix multiplier ignoring 0 (defaulting to 100).
- 8624184: Update styles, remove unused dependencies, and update the location in callback
- 148e9a0: update search to include relations
</details>

## 4.1.2 (2025-04-03 20:02:13)

<details>
<summary>Changes</summary>

### Patch Changes

- ac77d0d: Optimize manual grouping, improve wording/feedback, include additional fields.
</details>

## 4.1.1 (2025-04-03 19:42:11)

<details>
<summary>Changes</summary>

### Patch Changes

- 9ffb9c0: Handle the case that multiple criteria are matched, and filter out the ones not valid by checking effective date
- 2b89071: Use period_date for BenefitPoint applyToDate
</details>

## 4.1.0 (2025-04-03 19:16:59)

<details>
<summary>Changes</summary>

### Minor Changes

- 907e7e0: Add support to data actions tool for Array fields and operators (e.g. tags field)
### Patch Changes

- Updated dependencies [907e7e0]
  - common@0.9.0
</details>

## 4.0.2 (2025-04-03 07:12:14)

<details>
<summary>Changes</summary>

### Patch Changes

- de22227: Conditionally include accounting transactions in agents request to reduce payload
- 2786f93: Add a "Bank total" field and include it in the export. Also replace the file path, optimise the upload date type in the export with the filename.
- a327c5f: In referral calc, use 0 if it's set as a rate (previously would ignore 0 if specified).
- 45c61b8: Added comp calc fields to csv, excel and pdf export formatters
- ff85e35: Retrieve payment mode from reconciled policies when exporting commissions. Refactor/consolidate config for which keys pull from policy data.
- 205b60f: Fix the issue where the query chip becomes invalid when searching.
- 798ed11: Generate and save agentCommissionPayoutRate & agentPayoutRate in commission calc and store in real, editable columns.
- Updated dependencies [2786f93]
  - common@0.8.3
</details>

## 4.0.1 (2025-04-02 05:34:40)

<details>
<summary>Changes</summary>

### Patch Changes

- 8b624d3: Reduce pagination query size to 1000 for SmartOffice integration
- 00a160a: Fix for agent upline start/end dates defaulting to today (now null). Previously led to upline relationship not applying due to being applicable for only one day.
- af56926: In agent commissions log, rename "Agent split" to "Writing agent split" and remove code that updated this in each iteration, so we keep the original split.
- 59bc916: Allow user to manually group commission line items
- Updated dependencies [59bc916]
  - common@0.8.2
</details>

## 4.0.0 (2025-03-31 16:35:33)

<details>
<summary>Changes</summary>

### Major Changes
- 95e51a5: Add an auto-import workflow for document processing.
### Patch Changes

- 87cc1ee: Fix commissions not saving when comp_calc fields not configured for the account
</details>

## 3.6.2 (2025-03-31 05:42:36)

<details>
<summary>Changes</summary>

### Patch Changes

- 45ad4bd: Fix receivable-reconciliation agent/agency rates amplified issue
</details>

## 3.6.1 (2025-03-30 23:45:31)

<details>
<summary>Changes</summary>

### Patch Changes

- 920a29a: Get rid of reconciliation data source and replaced the widget
- Updated dependencies [920a29a]
  - common@0.8.1
</details>

## 3.6.0 (2025-03-28 21:42:13)

<details>
<summary>Changes</summary>

### Minor Changes

- cdf74b2: Create new commission fields for agent comp calc linked with accounting transactions
- 1ef516d: Made agent_payout_rate and agent_commission_payout_rate real db fields
- b1d37fd: Add support for bulk comp calculation from commissions page
### Patch Changes

- ab14b7a: Update reconciliation and comp calc to handle new `Offset` and `No payment` payout statuses (ignore)
- 9fa449b: Surface policy's geo state into commissions when null for comp report and csv exports
- 95b51a0: Apply multiplier to commission calc
- Updated dependencies [cdf74b2]
  - common@0.8.0
</details>

## 3.5.0 (2025-03-27 15:19:11)

<details>
<summary>Changes</summary>

### Minor Changes

- c832438: Add agent_payout_rate_override source from policy and alert message of no comp profile set up
</details>

## 3.4.5 (2025-03-27 15:12:44)

<details>
<summary>Changes</summary>

### Patch Changes

- 9bed6ec: Support new referral calc for product Carematters
- 5b45471: Similarity match group by
- 271303d: Revert relation joins
- bcae28c: Improve commission calc performance by separate grid rates query from the main query
- 5ca133d: Added limit concurrency to commission amount calculation on reconciliation page and export
- Updated dependencies [5b45471]
  - common@0.7.2
</details>

## 3.4.4 (2025-03-27 05:26:16)

<details>
<summary>Changes</summary>

### Patch Changes

- ceb804b: Include date params in comp calc activity logs.
</details>

## 3.4.3 (2025-03-26 15:57:55)

<details>
<summary>Changes</summary>

### Patch Changes

- 23da6a6: Fix commission calcs error caused by relation query of passing too many variables in querying contacts_agent_commission_schedule_profiles_sets
</details>

## 3.4.2 (2025-03-26 15:51:41)

<details>
<summary>Changes</summary>

### Patch Changes

- 2070a45: Force to re-initialize the OAuth2Client and set the refresh token
- 42a936c: Added 'Offset' and 'No payment' payout statuses
- 5f1d5ac: Handle widget crash
- aa9d112: Update echart label
- 66f44f6: Fix the error of injecting updated_at to query operation
- Updated dependencies [42a936c]
  - common@0.7.1
</details>

## 3.4.1 (2025-03-25 16:48:37)

<details>
<summary>Changes</summary>

### Patch Changes

- a29a86b: Add statement_amount to upload api
</details>

## 3.4.0 (2025-03-25 16:32:26)

<details>
<summary>Changes</summary>

### Minor Changes

- 3b411b5: Apply oneCommissionOneAgent with manual reconciliation
### Patch Changes

- db1f83d: Get rid of Policies processing date data source
</details>

## 3.3.1 (2025-03-24 21:39:37)

<details>
<summary>Changes</summary>

### Patch Changes

- Updated dependencies [e53c65a]
  - common@0.7.0
</details>

## 3.3.0 (2025-03-24 15:14:21)

<details>
<summary>Changes</summary>

### Minor Changes

- 8b38d1b: Add “Add condition” for all custom methods
### Patch Changes

- f0a09d9: Add updateTimestamp extension to automatically update updated_at timestamp and support the create/update operation to automatically inject created_by, created_proxied_by, updated_by, updated_proxied_by if necessary
</details>

## 3.2.6 (2025-03-21 18:32:22)

<details>
<summary>Changes</summary>

### Patch Changes

- 1af5603: Dashboard UX improvements
- 5b93d46: Update SmartOfficeWorker to comply with SmartOffice integration rules
</details>

## 3.2.5 (2025-03-21 17:19:54)

<details>
<summary>Changes</summary>

### Patch Changes

- e6b12a1: Adding retry and remove unneeded try/catch
</details>

## 3.2.4 (2025-03-21 14:11:35)

<details>
<summary>Changes</summary>

### Patch Changes

- Updated dependencies [33e0f17]
  - common@0.6.2
</details>

## 3.2.3 (2025-03-21 09:43:32)

<details>
<summary>Changes</summary>

### Patch Changes

- 7eeb09a: Fix circular dependency error
- 0b9967d: Switch to ipipeline_policies for TransGlobal worker
</details>

## 3.2.2 (2025-03-21 04:07:02)

<details>
<summary>Changes</summary>

### Patch Changes

- 1d60ce6: Use contact formatter
</details>

## 3.2.1 (2025-03-21 02:40:26)

<details>
<summary>Changes</summary>

### Patch Changes

- c620c6d: Implement feature for multiple data field calculation
</details>

## 3.2.0 (2025-03-20 20:06:01)

<details>
<summary>Changes</summary>

### Minor Changes

- 7b3775d: Document group page: added new options for group by, added total row and few small ui/ux improvements
### Patch Changes

- 0e1a221: Add a low credits notification for ExtractTable.
- 2ba163e: Fix payment date range query error in commission data view caused by passing Invalid Date string to server
- 81e0aff: Agent payout rate formatter aligned with exports and app view
- Updated dependencies [2ba163e]
  - common@0.6.1
</details>

## 3.1.1 (2025-03-20 09:54:56)

<details>
<summary>Changes</summary>

### Patch Changes

- 95a6cf0: Support Agent production and commissions
Data field aggregator update
</details>

## 3.1.0 (2025-03-20 09:54:56)

<details>
<summary>Changes</summary>

### Minor Changes

- 8d2ee05: Data update: Allow data actions to set fields based on other fields
### Patch Changes

- Updated dependencies [8d2ee05]
  - common@0.6.0
</details>

## 3.0.0 (2025-03-19 13:45:59)

<details>
<summary>Changes</summary>

### Major Changes
- 9fb1fcd: Fix bug for logic of alert message for comp calculation
### Minor Changes

- 76e90d8: Add checkbox up_to_received_rate
### Patch Changes

- b6b477c: Fixed value saved for policy agent commission rate overide
</details>

## 2.7.0 (2025-03-18 15:34:10)

<details>
<summary>Changes</summary>

### Minor Changes

- 2577eb2: implement getting rates for receivable reconciliation
### Patch Changes

- d4f32fc: Validate statement status before doing RiskTag sync statement
- 99be7b8: Show expected receivables for commission line items
- f951276: Update data update tool to handle rounding when working with numeric operators
- e5cf40f: Align payable commission calc criterial filtering logic with receivable calc by removing checking on product type
- 61b3d9c: Log out user only session expires, 401 errors will check if there'are logout action included
- 315a86e: Data update global config UI tweaks
- a72a995: Fixed global config not showing up for data update process
- 28d8f31: Filter agent level that with empty product_type or matched product_type
- 0096d75: Add support for matcher method: IS_WRITING_AGENT
- a558675: Fixed contact formatter not rendering nicknames for transglobal account
- c3de931: Don't return deleted accounts and roles on login
- 4fe0ba5: Throw BusinessException so that the client can see what is going wrong
- d04bb89: Fixed contacts referrals update not working
- Updated dependencies [f951276]
- Updated dependencies [0096d75]
  - common@0.5.2
</details>

## 2.6.5 (2025-03-13 18:53:44)

<details>
<summary>Changes</summary>

### Patch Changes

- 8ddd6d7: AgencyIntegrator worker now populates annualized revenue to premium_amount
- 3f4cc8a: Improved FE performance for data update tool and added pagination to preview results.
</details>

## 2.6.4 (2025-03-13 04:14:29)

<details>
<summary>Changes</summary>

### Patch Changes

- 15278c7: Fix Admin -> Companies -> Fintary company selector
</details>

## 2.6.3 (2025-03-13 04:08:15)

<details>
<summary>Changes</summary>

### Patch Changes

- c1bb228: Set details type to payable in commission calcs
- fb3ede5: Lint without fix when checking on the commit message stage
- 3d10000: lint before running git commit
- 2995fd5: Fix bug when editing saved reports data
- 32099f7: Refactor csv export generation for reconciliation data
- fec2b7b: Add IP whitelist verification
</details>

## 2.6.2 (2025-03-10 21:25:00)

<details>
<summary>Changes</summary>

### Patch Changes

- 544349d: Support multiple values for 'Contained in/ Not contained in' operators for FieldMatcher
- 3e7f4db: Comp grid viewer fix for not being able to add or edit comp grids and criteria that doesn't have any rates
</details>

## 2.6.1 (2025-03-10 06:20:30)

<details>
<summary>Changes</summary>

### Patch Changes

- ae1842f: Renamed DB field users_public_id to str_id
- 0cd1cb9: Auto assign contacts with policies while syncing policies
- 6e5a617: fix signup failure caused by updating new user that not created yet
</details>

## 2.6.0 (2025-03-06 18:02:35)

<details>
<summary>Changes</summary>

### Minor Changes

- cea4b59: Support session management for each account
### Patch Changes

- 850ae3c: The full run option is enabled for TransGlobal worker
</details>

## 2.5.0 (2025-03-05 19:22:45)

<details>
<summary>Changes</summary>

### Minor Changes

- 7705538: Added Update policy with payout rates button to commissions context dropdown
### Patch Changes

- d2ea7d8: Fix csv and excel commission exports agent commissions mismatch
- 850017f: OneHq now syncs contract levels with relation to compannies
</details>

## 2.4.2 (2025-03-05 03:03:11)

<details>
<summary>Changes</summary>

### Patch Changes

- 98f4bd5: Standarize agent commissions export in csv files
- f6eeeb0: fix bulk manual reconcile of only updating the contacts of the last statement
</details>

## 2.4.1 (2025-03-04 00:12:56)

<details>
<summary>Changes</summary>

### Patch Changes

- a62ff16: Access control for dashboard
</details>

## 2.4.0 (2025-03-02 23:42:39)

<details>
<summary>Changes</summary>

### Minor Changes

- 6996aaa: Show accounting transactions in agent page for producers
### Patch Changes

- 122f42d: Update BrokersAlliance carriers whitelist mapping
</details>

## 2.3.3 (2025-02-27 20:08:18)

<details>
<summary>Changes</summary>

### Patch Changes

- fa8f40d: Fix BGA commission calc failure Commission caused by no comp grid criteria
</details>

## 2.3.2 (2025-02-27 07:53:43)

<details>
<summary>Changes</summary>

### Patch Changes

- c670f09: Supports accountId & state injection for groupBy , findFirstOrThrow and aggregate
</details>

## 2.3.1 (2025-02-27 07:29:57)

<details>
<summary>Changes</summary>

### Patch Changes

- a6b8ec4: Hard code logic for TransGlobal to run specific grouping config
</details>

## 2.3.0 (2025-02-27 07:20:21)

<details>
<summary>Changes</summary>

### Minor Changes

- 53dab7c: Performance improvements for data update tool
### Patch Changes

- 45ea59e: Fix the issue of not returning grouped data caused by not checking nested state conditions
</details>

## 2.2.4 (2025-02-26 05:43:37)

<details>
<summary>Changes</summary>

### Patch Changes

- e843758: fix NowCerts post processing task failure
</details>

## 2.2.3 (2025-02-25 07:30:51)

<details>
<summary>Changes</summary>

### Patch Changes

- ca239ed: Feature: only sync whitelist carriers for BrokersAlliace
- 5db7d0e: Fix BuddyIns commission calc failure by setting accountInject flag in worker mode
- 01946c5: Bypass accoutInject
- b60bd1c: Removed '\*' from fields from policies in report exports
- 2b6c5eb: Feature: allow client uploading file with external_company_id instead of company name
- 3627cca: fix widgets loading isssue by removing findUnique support for now, it needs extra work
- Updated dependencies [2b6c5eb]
  - common@0.5.1
</details>

## 2.2.2 (2025-02-24 08:51:42)

<details>
<summary>Changes</summary>

### Patch Changes

- 1237c2f: Fix data syncing failure using worker caused by account inject
- eaad083: Feature: add state filter extension
</details>

## 2.2.1 (2025-02-24 05:32:41)

<details>
<summary>Changes</summary>

### Patch Changes

- 2540bcf: fix failure of commission calc using worker by disabling accountInject
</details>

## 2.2.0 (2025-02-24 04:05:17)

<details>
<summary>Changes</summary>

### Minor Changes

- 1d6ffcc: Feature: refactor MyAdvisorGridsWorker to sync carriers with carrier_ai_id, set these carrier's sync_worker to AgencyIntegrator
</details>

## 2.1.2 (2025-02-21 21:08:23)

<details>
<summary>Changes</summary>

### Patch Changes

- 717c096: Replaced DB queries in formatter for single queries in csv exports
</details>

## 2.1.1 (2025-02-20 03:15:02)

<details>
<summary>Changes</summary>

### Patch Changes

- bb06ce4: Added gross commission column to comp report excel summary page and removed not applicable totals columns
</details>

## 2.1.0 (2025-02-18 20:52:41)

<details>
<summary>Changes</summary>

### Minor Changes

- cd0cdc8: Support 'use policy data' option for commission items in data update tool
### Patch Changes

- Updated dependencies [cd0cdc8]
  - common@0.5.0
</details>

## 2.0.0 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Major Changes
- 3a6ec58: Added new comp reports approval workflow and accounting transactions integration
### Patch Changes

- dd89aa4: Block edit commissions on reviewed status and don't allow enable accounting transactions if per agent payout option is not enabled
- 6f65473: Default url not include params for dashboard
- Updated dependencies [3a6ec58]
  - common@0.4.2
</details>

## 1.10.1 (2025-02-13 23:35:47)

<details>
<summary>Changes</summary>

### Patch Changes

- 1016a22: Enhance share override to grid level for entities in the same chain
</details>

## 1.10.0 (2025-02-13 22:38:55)

<details>
<summary>Changes</summary>

### Minor Changes

- bae82d0: Implemented delete individual reports from report groups summary page
### Patch Changes

- ec72830: Move totals row to be before the data in comp reports excel export for commission and premium amount
</details>

## 1.9.3 (2025-02-13 08:12:42)

<details>
<summary>Changes</summary>

### Patch Changes

- b405005: Fix syncing agents for onehq worker caused by schema changing & disable agentLevel syncing for now
</details>

## 1.9.2 (2025-02-13 06:45:25)

<details>
<summary>Changes</summary>

### Patch Changes

- 6d9a483: BenefitPoint Worker now supports syncing acounts with type of prospect & client
</details>

## 1.9.1 (2025-02-13 01:29:52)

<details>
<summary>Changes</summary>

### Patch Changes

- f8bb29c: Return only comp grids and comp grid criteria with existing rates for comp grid viewer
- cf87da8: Fix syncing error caused by null values
</details>

## 1.9.0 (2025-02-11 21:19:19)

<details>
<summary>Changes</summary>

### Minor Changes

- e9ab407: Support for all operators in the data update tool
### Patch Changes

- ea72ed9: Fix the issue that manual reconcile doesn't populate contacts
- Updated dependencies [e9ab407]
  - common@0.4.1
</details>

## 1.8.9 (2025-02-10 08:38:26)

<details>
<summary>Changes</summary>

### Patch Changes

- 93cd627: AgencyIntegrator assigns agents to policies based on agent_code
</details>

## 1.8.8 (2025-02-10 01:29:02)

<details>
<summary>Changes</summary>

### Patch Changes

- ad668c8: Fix dashboard shows account data to producers
</details>

## 1.8.7 (2025-02-08 19:30:42)

<details>
<summary>Changes</summary>

### Patch Changes

- 5ee03d5: Fixed scheduled syncing failure caused by connecting undefined user
</details>

## 1.8.6 (2025-02-03 23:28:57)

<details>
<summary>Changes</summary>

### Patch Changes

- 3c5e021: Fixed comp grid level mapping when creating new rates from comp grid viewer.
</details>

## 1.8.5 (2025-01-30 05:32:50)

<details>
<summary>Changes</summary>

### Patch Changes

- 8a742d5: SSO error handling
</details>

## 1.8.4 (2025-01-28 19:01:56)

<details>
<summary>Changes</summary>

### Patch Changes

- 6b094c4: fix: transglobal sso
</details>

## 1.8.3 (2025-01-27 03:27:44)

<details>
<summary>Changes</summary>

### Patch Changes

- b024bdd: Fix the issue of not displaying customer name caused by missing type & company name
</details>

## 1.8.2 (2025-01-24 20:25:47)

<details>
<summary>Changes</summary>

### Patch Changes

- 1c5c1a7: Limit access to producer for dashboard
</details>

## 1.8.1 (2025-01-24 05:23:00)

<details>
<summary>Changes</summary>

### Patch Changes

- 8bc2baf: For Transglobal syncing documents from AWS, attach documents to North American
</details>

## 1.8.0 (2025-01-23 18:44:56)

<details>
<summary>Changes</summary>

### Minor Changes

- 299a16f: Feature: allow manually reconcile in the commission data view
### Patch Changes

- Updated dependencies [299a16f]
  - common@0.4.0
</details>

## 1.7.8 (2025-01-15 14:13:19)

<details>
<summary>Changes</summary>

### Patch Changes

- 3370cb6: Fix comp calc failure caused by undefined product
</details>

## 1.7.7 (2025-01-14 07:45:28)

<details>
<summary>Changes</summary>

### Patch Changes

- 414d4d9: Fix reconcile failure with disable update contacts of approved/paid statements
- 7482291: Fixed the Broker Alliance comp calc failure caused by wrong parallel level for PolicyScope calculation
</details>

## 1.7.6 (2025-01-08 14:34:33)

<details>
<summary>Changes</summary>

### Patch Changes

- 75369d9: AgencyIntegrator now populates PlacedDate to policy_date
</details>

## 1.7.5 (2025-01-07 08:30:58)

<details>
<summary>Changes</summary>

### Patch Changes

- 77403d8: Query policy data include effective date with null by default
</details>

## 1.7.4 (2025-01-07 08:06:27)

<details>
<summary>Changes</summary>

### Patch Changes

- a7e9a95: RiskTag data syncing now populates policy type to product sub type field base on product type
- Updated dependencies [a7e9a95]
  - common@0.3.2
</details>

## 1.7.3 (2024-12-28 09:07:51)

<details>
<summary>Changes</summary>

### Patch Changes

- 9e039c6: AgencyIntegator worker now populates personKey to agent_code
</details>

## 1.7.2 (2024-12-23 10:59:48)

<details>
<summary>Changes</summary>

### Patch Changes

- 8a711e9: Fix AgencyIntegrator agents syncing failure caused by passing wrong data type of zip
</details>

## 1.7.1 (2024-12-23 04:08:56)

<details>
<summary>Changes</summary>

### Patch Changes

- d26945a: AgencyIntegrator worker now supports syncing specified policies
- Updated dependencies [d26945a]
  - common@0.3.1
</details>

## 1.7.0 (2024-12-22 07:20:38)

<details>
<summary>Changes</summary>

### Minor Changes

- cf24277: Cache decorator support parameters
### Patch Changes

- f3bd7e4: Add permission check to documents endpoint and export endpoint
</details>

## 1.6.2 (2024-12-20 14:29:38)

<details>
<summary>Changes</summary>

### Patch Changes

- 899a4f6: Refactor dashboard filter
</details>

## 1.6.1 (2024-12-17 10:47:02)

<details>
<summary>Changes</summary>

### Patch Changes

- 9176e5b: Fix sync icon displaying issue for uplines caused by not returning the config fields
- 7399936: Fix the issue of not serving page caused by copying static files into wrong directory
</details>

## 1.6.0 (2024-12-16 07:22:50)

<details>
<summary>Changes</summary>

### Minor Changes

- bdef42d: Add openapi to allow user to upload a document via openapi calls
### Patch Changes

- Updated dependencies [bdef42d]
  - common@0.3.0
</details>

## 1.5.5 (2024-12-13 20:24:24)

<details>
<summary>Changes</summary>

### Patch Changes

- ae1330f: Fix the issue that the OneHQ worker didn't populate business age to the note field
</details>

## 1.5.4 (2024-12-12 13:08:58)

<details>
<summary>Changes</summary>

### Patch Changes

- 57a4e1c: Add alert in commission log for agents with no matching comp profiles
</details>

## 1.5.3 (2024-12-12 08:24:16)

<details>
<summary>Changes</summary>

### Patch Changes

- 2b8e359: Properly handle exceptions, and hide exception details from the user
</details>

## 1.5.2 (2024-12-12 08:18:37)

<details>
<summary>Changes</summary>

### Patch Changes

- 9300410: AgencyIntragorWorker now syncs first agent, second agent and third agent with policies
</details>

## 1.5.1 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- d4aee1a: BugFix: validate data range in compProfileAdd component, start date should less than end date
</details>

## 1.5.0 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Minor Changes

- 7849a5b: Feature: settle long running data processing tasks via schedule task checking
</details>

## 1.4.2 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- 7408934: BugFix: revive proxy user for data processing tasks
</details>

## 1.4.1 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- 4e95900: BugFix: set parallel level to 1 for DMI, which uses the policy scope calcs
</details>

## 1.4.0 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Minor Changes

- 8a5ccb0: Feature: add AwsS3Worker to sync documents from aws storage
### Patch Changes

- Updated dependencies [8a5ccb0]
  - common@0.2.0
</details>

## 1.3.0 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Minor Changes

- 1cbc8a5: Feat: support multi workers & integrate MAG syncing service
</details>

## 1.2.1 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- e3fb280: Version: bump node version to v22.11.0
</details>

## 1.2.0 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Minor Changes

- 667d7c3: Feature: add compensation type condition
### Patch Changes

- Updated dependencies [667d7c3]
  - common@0.1.0
</details>

## 1.1.2 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- 681e06b: Add tags filter to commission page
- Updated dependencies [681e06b]
  - common@0.0.2
</details>

## 1.1.1 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- 2f334ab: Create widget support group by agents
Create filter by tags for TAG
</details>

## 1.1.0 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Minor Changes

- 49a8f9f: Add support for stacked charts (carriers only)
### Patch Changes

- 49a8f9f: Add 'Enable updates' toggle for user impersonation (CSMs exempt)
</details>

## 1.0.7 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- 3b9fff9: Feature: SmartOffice integration: support syncing policies, agents, companies and products
</details>

## 1.0.6 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- 93b7f31: Feature: [AgencyIntegrator] populate product name into policy
</details>

## 1.0.5 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- 8d2b1f6: BugFix: Fix failure of updating agent when adding new contact_levels & uplines
</details>

## 1.0.4 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- 39ad848: Feature: support overriding synced contact_levels & contact_hierarchy
- 7f8eeeb: Fix: decimal comparison failure because of precision
</details>

## 1.0.3 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- ddaac06: Fix: agents filter in policy data view
- Updated dependencies [ddaac06]
  - common@0.0.1
</details>

## 1.0.2 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- 57d682c: BugFix: reconcilation failure due to updating with large dataset, fix by updating by batching
</details>

## 1.0.1 (2025-02-17 20:41:52)

<details>
<summary>Changes</summary>

### Patch Changes

- 0becf97: Web version checker:
  Notify user to refresh app when there are newer version
- b11e9bd: AgencyIntegrator worker enhancement:
  - Enable iterative syncing, can run a full sync by select the full run option
  - Policy syncing doens't need commission data anymore
- Updated dependencies [e461619]
- Updated dependencies [b11e9bd]
  - common@0.0.0
</details>

</details>
