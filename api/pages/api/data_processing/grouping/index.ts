import * as Sentry from '@sentry/nextjs';
import { AccountIds } from 'common/constants';
import * as dto from 'common/dto/data_processing/grouping';
import { chunk } from 'lodash-es';
import { nanoid } from 'nanoid';
import { NextApiRequest, NextApiResponse } from 'next';
import { create<PERSON><PERSON><PERSON>, Post, Req, Res } from 'next-api-decorators';
import { report_data, statement_data } from '@prisma/client';
import { GroupingRule } from 'common/dto/data_processing/grouping-rules';

import { container } from '@/ioc';
import { AccountInfo, ZodBody } from '@/lib/decorators';
import { arrayToMap, limitConcurrency, runInBatch } from '@/lib/helpers';
import { withAuth } from '@/lib/middlewares';
import { prismaClient } from '@/lib/prisma';
import { COMMISSION_STATUS_EDITABLE } from '@/pages/api/data_processing/commissions/commissions.constant';
import { Prisma } from '@/prisma';
import { DataProcessService } from '@/services/data_processing';
import { Guard } from '@/services/permission/decorator';
import { CommonAction, EntityType } from '@/services/permission/interface';
import { QueueService } from '@/services/queue';
import { Queue } from '@/services/queue/types';
import {
  DataLinkingConfig,
  DataLinkingConfigResult,
  DataProcessingStatuses,
  DataProcessingTypes,
  DataStates,
  ExtAccountInfo,
  ExtNextApiRequest,
  ExtNextApiResponse,
  Tables,
} from '@/types';
import { BaseHandler } from '@/lib/baseHandler';
import { GroupDedupeService } from '@/services/data_processing/group-dedupe';
import { GroupingRulesService } from '@/services/data_processing/grouping-rules';

class Handler extends BaseHandler {
  @Post()
  @Guard(CommonAction.RUN, EntityType.SETTINGS_DATA_PROCESSING)
  async dedupeGroupData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse,
    @AccountInfo() account: ExtAccountInfo,
    @(ZodBody(dto.GroupingDTOSchema)()) body: dto.GroupingDTO
  ) {
    if (body.isSync) {
      return await dedupeGroupData(account, body, req, res);
    }
    const queueService = container.get<QueueService>(QueueService);
    const params = {
      account,
      type: DataProcessingTypes.grouping,
      queue: Queue.GROUP_DEDUPE,
      url: process.env.CLOUD_WORKER_URL,
      payload: {
        ...body,
      },
    };
    const taskId = await queueService.createTask<dto.GroupingDTO>(params);
    res.status(200).json({ success: true, taskId });
  }
}

export default withAuth(createHandler(Handler));

const TRANSGLOBAL_CARRIER_COMMISSION_GROUPING_WHITELIST = [
  'National Life Insurance Co.',
  'National Life Insurance Company',
  'National Life of Vermont',
  'Life Insurance Co of SW',
];

export const dedupeGroupData = async (
  account: ExtAccountInfo,
  body: dto.GroupingDTO,
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  let dataProcessing;
  const startTime = Date.now();
  try {
    req.logger.profile('deDupeGroupData()');
    const {
      reportDeDupeConfig,
      reportGroupingConfig,
      reportGrouping2Config,
      statementDeDupeConfig,
      statementGroupingConfig,
    } = await getAllData({ account_id: account.account_id });
    const groupingRulesService =
      container.get<GroupingRulesService>(GroupingRulesService);
    const groupDedupeService =
      container.get<GroupDedupeService>(GroupDedupeService);
    const dataProcessingService =
      container.get<DataProcessService>(DataProcessService);
    dataProcessing = await (body.taskId
      ? dataProcessingService.updateTaskStatus({
          str_id: body.taskId,
          status: DataProcessingStatuses.PROCESSING,
        })
      : dataProcessingService.create({
          str_id: nanoid(),
          account: { connect: { str_id: account.account_id } },
          user: { connect: { uid: account.uid } },
          proxy_user: req.ouid ? { connect: { uid: req.ouid } } : undefined,
          type: DataProcessingTypes.grouping,
          status: DataProcessingStatuses.PROCESSING,
        }));

    const result: { [key: string]: DataLinkingConfigResult } = {};

    let processes: {
      id: string;
      configs: DataLinkingConfig[];
      table: Tables;
      action: 'group' | 'dedupe';
      linkedState: DataStates;
      sourceStates: DataStates[];
    }[] = [
      {
        id: 'policyDeDupe',
        configs: [reportDeDupeConfig],
        table: Tables.POLICY_DATA,
        action: 'dedupe',
        linkedState: DataStates.DUPLICATE,
        sourceStates: [DataStates.DUPLICATE, DataStates.GROUPED],
      },
      {
        id: 'policyGrouping',
        configs: [reportGroupingConfig, reportGrouping2Config],
        table: Tables.POLICY_DATA,
        action: 'group',
        linkedState: DataStates.GROUPED,
        sourceStates: [DataStates.GROUPED],
      },
      {
        id: 'commissionDeDupe',
        configs: [statementDeDupeConfig],
        table: Tables.COMMISSION_DATA,
        action: 'dedupe',
        linkedState: DataStates.DUPLICATE,
        sourceStates: [DataStates.DUPLICATE],
      },
      {
        id: 'commissionGrouping',
        configs: [statementGroupingConfig],
        table: Tables.COMMISSION_DATA,
        action: 'group',
        linkedState: DataStates.GROUPED,
        sourceStates: [DataStates.GROUPED],
      },
    ];

    // Filter processes based on statementIds and reportIds
    if (body.statementIds?.length || body.reportIds?.length) {
      processes = processes.filter((process) => {
        if (
          body.statementIds?.length &&
          process.table === Tables.COMMISSION_DATA &&
          body.actions.includes(process.action)
        ) {
          return true;
        }
        if (
          body.reportIds?.length &&
          process.table === Tables.POLICY_DATA &&
          body.actions.includes(process.action)
        ) {
          return true;
        }
        return false;
      });
    }

    for await (const process of processes) {
      const isCarrierInWhitelist =
        process.table === Tables.COMMISSION_DATA &&
        account.account_id === AccountIds.TRANSGLOBAL;

      const isInvalidConfig =
        process.configs.length === 0 ||
        !process.configs.some(
          (config) =>
            config.fields?.length > 0 &&
            !(config.fields?.length === 1 && config.fields[0] === 'id')
        );

      req.logger.info(`Starting linking process: ${process.id}`);
      console.group();
      const startTime = Date.now();
      // TODO: Fix this TypeScript error / remove "as any"
      req.logger.info(`Getting ${process.table} data`);
      let lastId = 0;
      const data = [];
      let hasMore = true;
      const where:
        | Prisma.statement_dataWhereInput
        | Prisma.report_dataWhereInput = {
        account_id: account.account_id,
        state: { in: [...process.sourceStates, DataStates.ACTIVE] },
        id: { gt: lastId },
      };
      if (process.table === Tables.COMMISSION_DATA) {
        (where as Prisma.statement_dataWhereInput).allocated_amount = null;
      }
      if (body.isManual) {
        if (body.statementIds && process.table === Tables.COMMISSION_DATA) {
          where.id = { in: body.statementIds };
        }
        if (body.reportIds && process.table === Tables.POLICY_DATA) {
          where.id = { in: body.reportIds };
        }
      }
      if (body.excludeNonEditable && process.table === Tables.COMMISSION_DATA) {
        where.OR = COMMISSION_STATUS_EDITABLE;
      }
      if (
        (body.startDate || body.endDate) &&
        process.table === Tables.COMMISSION_DATA
      ) {
        (where as Prisma.statement_dataWhereInput).processing_date = {
          gte: body.startDate ? new Date(body.startDate) : undefined,
          lte: body.endDate ? new Date(body.endDate) : undefined,
        };
      }
      if (isCarrierInWhitelist) {
        (where as Prisma.statement_dataWhereInput).carrier_name = {
          in: TRANSGLOBAL_CARRIER_COMMISSION_GROUPING_WHITELIST,
        };
      }
      while (hasMore) {
        const result = await (prismaClient[process.table] as any).findMany({
          where,
          include:
            process.table === Tables.POLICY_DATA
              ? { statement_data: true, children_report_data: true }
              : { children_data: true },
          orderBy: { id: 'asc' },
          take: 10000,
        });
        req.logger.info(
          `Table ${process.table}: Fetched ${result.length} records`
        );
        data.push(...result);
        if (result.length < 10000) {
          hasMore = false;
        } else {
          lastId = result[result.length - 1].id;
          where.id = { gt: lastId };
        }
      }
      req.logger.info(`Data source: ${process.table} (${data.length} records)`);
      const dataMap = arrayToMap(data, 'id');
      let curData = data;
      const rulesFromDb = await groupingRulesService.getRules();

      for await (const [configIndex, config] of process.configs.entries()) {
        const policyDataPriorityField = config?.priority_field ?? 'created_at';

        let dupeIds: number[];
        let relations: Map<number, (statement_data | report_data)[]>;
        let virtualRecords: Map<number, statement_data | report_data>;

        if (body.isManual) {
          const result = await groupDedupeService.manualDedupe(
            curData,
            body.masterId,
            {
              accountId: account.account_id,
              useVirtualRecords: body.useVirtualRecords,
              calculationMethod: body.calculationMethod,
            }
          );
          dupeIds = result.ids;
          relations = result.relations;
          virtualRecords = result.virtualRecords;
        } else {
          let prioritySortFn = (a, b) =>
            b.created_at.getTime() - a.created_at.getTime();
          if (
            process.action === 'dedupe' ||
            process.table === Tables.POLICY_DATA
          ) {
            if (isInvalidConfig) {
              req.logger.info(
                `Skipping linking process (no config): ${process.id}`
              );
              continue;
            }
            if (policyDataPriorityField === 'aggregation_primary') {
              prioritySortFn = (a, b) => {
                return a.aggregation_primary ? -1 : 1;
              };
            }
            const dupedResult = await groupDedupeService.findDupes(
              curData,
              config,
              {
                table: process.table,
                prioritySortFn: prioritySortFn,
                useVirtualRecords: false,
              }
            );
            relations = dupedResult.relations;
            dupeIds = dupedResult.ids;
          } else {
            const rules = rulesFromDb.filter(
              (r) => r.entity === process.table
            ) as GroupingRule[];
            const dupedResult = await groupDedupeService.findDupesWithRules(
              curData,
              rules,
              {
                table: process.table,
                prioritySortFn: prioritySortFn,
                accountId: account.account_id,
                useVirtualRecords: body.useVirtualRecords,
              }
            );
            relations = dupedResult.relations;
            dupeIds = dupedResult.ids;
            virtualRecords = dupedResult.virtualRecords;

            if (!isInvalidConfig) {
              // Remaining data will go through the normal findDupe
              const dupedResult2 = await groupDedupeService.findDupes(
                dupedResult.remainingData,
                config,
                {
                  table: process.table,
                  prioritySortFn: prioritySortFn,
                  useVirtualRecords: body.useVirtualRecords,
                }
              );
              // Merge virtualRecords from both results
              virtualRecords = new Map([
                ...dupedResult.virtualRecords,
                ...dupedResult2.virtualRecords,
              ]);
              dupeIds = [...dupedResult.ids, ...dupedResult2.ids];
              relations = new Map([
                ...dupedResult.relations,
                ...dupedResult2.relations,
              ]);
            }
          }
        }
        req.logger.info(`Found ${dupeIds.length} linked records`);

        curData = curData.filter((e) => !dupeIds.includes(e.id));

        // These two should be the same...keep the latter one since it's simpler
        const idsToActivate = curData
          .filter(
            (row) =>
              process.sourceStates.includes(row.state) &&
              !dupeIds.includes(row.id)
          )
          .map((row) => row.id);

        req.logger.info(
          `Activating records that were previously linked (${idsToActivate.length}): ${idsToActivate.join(', ')}`
        );

        const MAX_BIND_VIRABLES = 10000;
        const groupTasks = chunk(idsToActivate, MAX_BIND_VIRABLES);
        await Promise.all(
          groupTasks.map(async (ids) =>
            (prismaClient[process.table] as any).updateMany({
              where: {
                account_id: req.account_id,
                state: process.linkedState,
                id: { in: ids },
              },
              data: {
                state: 'active',
                parent_id: null,
              },
            })
          )
        );

        // Unlink previous master records from their children
        const previousMasterRecords = Array.from(relations.values()).flatMap(
          (items: any[]) =>
            items
              .filter((data) =>
                process.table === Tables.POLICY_DATA
                  ? data?.children_report_data?.length
                  : data?.children_data?.length
              )
              .map((r) => r.id)
        );

        await runInBatch({
          items: previousMasterRecords,
          onBatch: async (masterIds: number[]) => {
            await (prismaClient[process.table] as any).updateMany({
              where: {
                account_id: account.account_id,
                parent_id: { in: masterIds },
              },
              data: { parent_id: null, state: DataStates.ACTIVE },
            });
            return [];
          },
          batchSize: 1000,
        });

        req.logger.info('Linking linked records to their master record');
        console.group();

        // Update parent child relations
        await limitConcurrency(
          async (relation) => {
            const [parentId, children] = relation;
            const parent = dataMap[parentId] || virtualRecords.get(parentId);
            req.logger.info(
              `Linking children to parent: parent id ${parentId}, children ids: ${children.map((c) => c.id).join(', ')}`
            );
            if (!parent) {
              console.error('Parent not found', parentId);
              return;
            }
            await (prismaClient[process.table] as any).update({
              where: { account_id: account.account_id, id: parentId },
              data: {
                state: DataStates.ACTIVE,
                parent_id: null,
              },
            });
            await (prismaClient[process.table] as any).updateMany({
              where: {
                account_id: account.account_id,
                id: { in: children.map((c) => c.id) },
              },
              data: {
                state: process.linkedState,
                parent_id: parentId,
              },
            });
          },
          Array.from(relations.entries()),
          10
        );
        const { count } = await groupDedupeService.deleteOrphanVirtualRecords(
          process.table,
          account.account_id
        );
        req.logger.info(
          `Deleted ${count} orphan virtual records for ${process.table}`
        );

        console.groupEnd();

        const endTime = Date.now();
        result[`${process.id}::${configIndex}`] = {
          config: config,
          activeIdsCount: curData.length,
          linkedIdsCount: dupeIds.length,
          time: endTime - startTime,
        };
      }

      console.groupEnd();
    }

    req.logger.info('Result', result);

    const endTime = Date.now();

    dataProcessing = await prismaClient.data_processing.update({
      where: { id: dataProcessing.id },
      data: {
        status: DataProcessingStatuses.COMPLETED,
        stats: result as object,
        duration: endTime - startTime,
      },
    });

    if (body.isSync) {
      res.status(200).json({ data: result, statusText: 'ok' });
    }
  } catch (error) {
    req.logger.error(`Error deduping data: ${error.message}`, error);
    Sentry.captureException(error);
    const endTime = Date.now();
    if (dataProcessing?.id) {
      dataProcessing = await prismaClient.data_processing.update({
        where: { id: dataProcessing.id },
        data: {
          status: DataProcessingStatuses.ERROR,
          duration: endTime - startTime,
          stats: {
            error: error.message,
          },
        },
      });
    }
    if (body.isSync) {
      res.status(500).json({ error: error.message });
    }
  } finally {
    req.logger.profile('deDupeGroupData()');
  }
  return;
};

const getAllData = async ({
  account_id,
}): Promise<Record<string, DataLinkingConfig>> => {
  const accountSettings = await prismaClient.accounts.findFirst({
    where: { str_id: account_id, state: 'active' },
  });

  return {
    reportDeDupeConfig:
      accountSettings?.report_de_dupe as unknown as DataLinkingConfig,
    reportGroupingConfig:
      accountSettings?.report_grouping as unknown as DataLinkingConfig,
    reportGrouping2Config:
      accountSettings?.report_grouping2 as unknown as DataLinkingConfig,
    statementDeDupeConfig:
      accountSettings?.statement_de_dupe as unknown as DataLinkingConfig,
    statementGroupingConfig:
      accountSettings?.statement_grouping as unknown as DataLinkingConfig,
  };
};
