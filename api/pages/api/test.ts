// import { create<PERSON><PERSON><PERSON>, <PERSON> } from 'next-api-decorators';
// import { AccountIds } from 'common/constants';
// import { contacts, Prisma } from '@prisma/client';

// import { withLogger } from '@/lib/middlewares';
// import { asyncLocalStorage } from '@/services/logger/appLogger';
// import { prismaClient } from '@/lib/prisma';
// import { DataProcessingTypes, DataStates } from '@/types';
// import { container } from '@/ioc';
// import { AgencyIntegratorWorker } from '@/services/queue/worker/agencyIntegrator';
// import { limitConcurrency } from '@/lib/helpers';
// import {
//   InquiryLevel,
//   OperationTypeCodes,
// } from '@/services/agencyIntegrator/interfaces';
// import { SmartOfficeWorker } from '@/services/queue/worker/smartOffice';
// import { BenefitPointWorker } from '@/services/queue/worker/benefitPoint';
// import { MigrationService } from '@/services/migration';

// class Test {
//   // @Get()
//   async get() {
//     return await asyncLocalStorage.run({ traceId: '123' }, async () => {
//       const agentCodes = await prismaClient.contacts.findMany({
//         where: {
//           account_id: AccountIds.BROKERS_ALLIANCE,
//           state: DataStates.ACTIVE,
//           agent_code: {
//             not: null,
//           },
//         },
//         select: {
//           agent_code: true,
//           sync_id: true,
//           id: true,
//         },
//         orderBy: {
//           agent_code: 'asc',
//         },
//       });

//       // Group contacts by agent_code to find duplicates
//       const duplicateAgents = agentCodes.reduce((acc, contact) => {
//         if (!acc[contact.agent_code]) {
//           acc[contact.agent_code] = [];
//         }
//         acc[contact.agent_code].push(contact);
//         return acc;
//       }, {});

//       // Filter for agent_codes with multiple contacts
//       const duplicates = Object.entries(duplicateAgents)
//         .filter(([_, contacts]: [string, unknown[]]) => contacts.length > 1)
//         .reduce<Record<string, unknown[]>>((acc, [agent_code, contacts]) => {
//           return { ...acc, [agent_code]: contacts };
//         }, {});

//       const service = container.get<AgencyIntegratorWorker>(
//         AgencyIntegratorWorker
//       );
//       service.task = {
//         account: {
//           account_id: AccountIds.BROKERS_ALLIANCE,
//           uid: '123',
//           ouid: '123',
//           role_id: '123',
//         },
//         type: DataProcessingTypes.data_sync,
//         payload: duplicates,
//       };
//       await limitConcurrency(
//         async ([agentCode, contacts]: [string, contacts[]]) => {
//           const synced = contacts.find((c) => c.sync_id);
//           if (synced) {
//             await service.upsert('contacts', {
//               where: {
//                 sync_id: agentCode,
//                 account_id: AccountIds.BROKERS_ALLIANCE,
//                 ...synced,
//                 state: DataStates.ACTIVE,
//               },
//               update: {
//                 ...synced,
//               },
//               create: {
//                 ...synced,
//               },
//             });
//           }
//         },
//         Object.entries(duplicates),
//         300
//       );
//       return;
//     });
//   }

//   async setUpWorker<T>(w: any, accountId: string) {
//     const worker: any = container.get(w);
//     await worker.setup({
//       account_id: accountId,
//       uid: '123',
//       ouid: '123',
//       role_id: '123',
//     });
//     worker.task = {
//       account: {
//         account_id: accountId,
//         uid: '123',
//         ouid: '123',
//         role_id: '123',
//       },
//       type: DataProcessingTypes.data_sync,
//       payload: {},
//     };
//     return worker as T;
//   }
//   async brokers() {
//     return await asyncLocalStorage.run(
//       {
//         traceId: '123',
//         account: {
//           account_id: accountId,
//           uid: '123',
//           ouid: '123',
//           role_id: '123',
//         },
//       },
//       async () => {
//         const worker = container.get<AgencyIntegratorWorker>(
//           AgencyIntegratorWorker
//         );
//         await worker.setup({
//           account_id: AccountIds.BROKERS_ALLIANCE,
//           uid: '123',
//           ouid: '123',
//           role_id: '123',
//         });
//         worker.task = {
//           account: {
//             account_id: AccountIds.BROKERS_ALLIANCE,
//             uid: '123',
//             ouid: '123',
//             role_id: '123',
//           },
//           type: DataProcessingTypes.data_sync,
//           payload: {},
//         };
//         const result = await worker.agencyIntegratorService.policySearch({
//           inquiryLevel: InquiryLevel.All,
//           filters: [
//             {
//               ObjectType: { '@tc': 18, '#text': 'Policy' },
//               PropertyName: 'PolNumber',
//               PropertyValue: '**********', // 'LZ774967',
//               Operation: { '@tc': OperationTypeCodes.Equal },
//             },
//           ],
//         });
//         const data = await worker.agencyIntegratorService.partySearch({
//           inquiryLevel: InquiryLevel.BasicWithRelations,
//           filters: [
//             {
//               ObjectType: { '@tc': 8, '#text': 'Relation' },
//               PropertyName: 'RelationRoleCode',
//               PropertyValue: 11,
//               Operation: { '@tc': OperationTypeCodes.Equal },
//             },
//             {
//               ObjectType: { '@tc': 115, '#text': 'Person' },
//               PropertyName: 'PersonKey',
//               PropertyValue: '23027119',
//               Operation: { '@tc': OperationTypeCodes.Equal },
//             },
//           ],
//         });
//         console.log(data);
//         // await worker.syncAgentSplits(result);
//         console.log(result);
//         return data;
//       }
//     );
//   }

//   canDedupe(
//     agent: Prisma.contactsGetPayload<{
//       include: {
//         contacts_agent_commission_schedule_profiles: true;
//         contacts_agent_commission_schedule_profiles_sets: true;
//         parent_relationships: true;
//         child_relationships: true;
//       };
//     }>
//   ) {
//     return (
//       agent.contacts_agent_commission_schedule_profiles.length === 0 &&
//       agent.parent_relationships.length === 0 &&
//       agent.child_relationships.length === 0 &&
//       agent.contacts_agent_commission_schedule_profiles_sets.length === 0
//     );
//   }

//   async dedupeSO() {
//     const accountId = 'EoSqZ1CPSt0GX56Eqt-Sx';
//     return asyncLocalStorage.run(
//       {
//         traceId: '123',
//         account: {
//           account_id: accountId,
//           uid: '123',
//           ouid: '123',
//           role_id: '123',
//         },
//       },
//       async () => {
//         const worker = await this.setUpWorker(SmartOfficeWorker, accountId);
//         const agentName = await prismaClient.contacts.findMany({
//           where: {
//             account_id: 'EoSqZ1CPSt0GX56Eqt-Sx',
//             state: DataStates.ACTIVE,
//             name: { not: null },
//           },
//           include: {
//             contacts_agent_commission_schedule_profiles: {
//               where: { state: DataStates.ACTIVE },
//             },
//             contacts_agent_commission_schedule_profiles_sets: {
//               where: { state: DataStates.ACTIVE },
//             },
//             parent_relationships: {
//               where: { state: DataStates.ACTIVE },
//             },
//             child_relationships: {
//               where: { state: DataStates.ACTIVE },
//             },
//           },
//           orderBy: {
//             sync_id: 'desc',
//           },
//         });

//         // Group contacts by agent_code to find duplicates
//         const duplicateAgents = agentName.reduce((acc, contact) => {
//           if (!acc[contact.name]) {
//             acc[contact.name] = [];
//           }
//           acc[contact.name].push(contact);
//           return acc;
//         }, {});

//         // Filter for agent_names with multiple contacts
//         const duplicates = Object.entries(duplicateAgents)
//           .filter(([_, contacts]: [string, unknown[]]) => contacts.length == 2)
//           .reduce<Record<string, unknown[]>>((acc, [name, contacts]) => {
//             return { ...acc, [name]: contacts };
//           }, {});

//         // Get all policies where contacts is not '{}'
//         // Check if contacts is an array or a JSON string
//         const policies = await prismaClient.report_data.findMany({
//           where: {
//             account_id: accountId,
//             state: {
//               in: [DataStates.ACTIVE, DataStates.GROUPED, DataStates.DUPLICATE],
//             },
//             contacts: {
//               isEmpty: false,
//             },
//           },
//           select: {
//             id: true,
//             contacts: true,
//           },
//         });

//         // Create a map of <contact_str_id, policies[]>
//         const contactToPoliciesMap = new Map<string, any[]>();

//         // Process each policy to extract contact references
//         policies.forEach((policy) => {
//           const contacts =
//             typeof policy.contacts === 'string'
//               ? JSON.parse(policy.contacts)
//               : policy.contacts;

//           if (typeof contacts === 'object' && contacts !== null) {
//             Object.keys(contacts).forEach((contactKey) => {
//               const contactId = contacts[contactKey];
//               if (!contactToPoliciesMap.has(contactId)) {
//                 contactToPoliciesMap.set(contactId, []);
//               }
//               contactToPoliciesMap.get(contactId).push(policy);
//             });
//           }
//         });

//         console.log(
//           `Found ${contactToPoliciesMap.size} unique contacts in policies`
//         );
//         let count1 = 0;
//         let count2 = 0;
//         await limitConcurrency(
//           async ([name, contacts]: [
//             string,
//             Prisma.contactsGetPayload<{
//               include: {
//                 contacts_agent_commission_schedule_profiles: true;
//                 contacts_agent_commission_schedule_profiles_sets: true;
//                 parent_relationships: true;
//                 child_relationships: true;
//               };
//             }>[],
//           ]) => {
//             // Find contact with matching agent_code but no sync_id
//             const existingWithoutSyncId = contacts.find(
//               (agent) => agent.sync_id === null
//             );
//             const syncedAgent = contacts.find((agent) => agent.sync_id);

//             // The deduping logic here only works if there are only two agents returned for a given agent_code
//             if (syncedAgent && existingWithoutSyncId) {
//               if (this.canDedupe(existingWithoutSyncId)) {
//                 console.log(
//                   `Deduping ${existingWithoutSyncId.id} because it has no commission profiles or uplines`
//                 );
//                 count1++;
//                 const policies = contactToPoliciesMap.get(
//                   existingWithoutSyncId.str_id
//                 );
//                 for (const policy of policies) {
//                   console.log(
//                     `Updating policy ${policy.id} to use synced agent from ${existingWithoutSyncId.str_id} to ${syncedAgent.str_id}`
//                   );
//                   const others = policy.contacts.filter(
//                     (contact) => contact !== existingWithoutSyncId.str_id
//                   );
//                   await prismaClient.report_data.update({
//                     where: { id: policy.id },
//                     data: {
//                       contacts: [...others, syncedAgent.str_id],
//                     },
//                   });
//                 }
//                 // Mark the existing manually created agent as deleted
//                 return await prismaClient.contacts.update({
//                   where: { id: existingWithoutSyncId.id },
//                   data: { state: DataStates.DELETED },
//                 });
//               } else {
//                 // If the existing manually created agent has commission profiles or uplines, and the synced agent can be deduped,
//                 // then mark the synced agent as deleted and update the existing manually created agent with the sync_id
//                 if (this.canDedupe(syncedAgent)) {
//                   count2++;
//                   console.log(
//                     `Deduping ${syncedAgent.id} to ${existingWithoutSyncId.id} because it has no commission profiles or uplines`
//                   );
//                   await prismaClient.contacts.update({
//                     where: { id: syncedAgent.id },
//                     data: { state: DataStates.DELETED, sync_id: null },
//                   });
//                   await prismaClient.contacts.update({
//                     where: { id: existingWithoutSyncId.id },
//                     data: { sync_id: syncedAgent.sync_id },
//                   });
//                   return existingWithoutSyncId;
//                 }
//               }
//             }
//           },
//           Object.entries(duplicates),
//           300
//         );
//         console.log(count1, count2);
//       }
//     );
//   }
//   async mergePolicyInfo() {
//     const accountId = 'EoSqZ1CPSt0GX56Eqt-Sx';
//     return asyncLocalStorage.run(
//       {
//         traceId: '123',
//         account: {
//           account_id: accountId,
//           uid: '123',
//           ouid: '123',
//           role_id: '123',
//         },
//       },
//       async () => {
//         // const worker = await this.setUpWorker<SmartOfficeWorker>(
//         //   SmartOfficeWorker,
//         //   accountId
//         // );
//         // const policies = await worker.smartOfficeService.getPolicies({
//         //   schema: {
//         //     UniqueID: null,
//         //     CarrierName: null,
//         //     PolicyNumber: null,
//         //     Age: null,
//         //     AnnualPlcyFee: null,
//         //     AnnualPremium: null,
//         //     BeneficaryName: null,
//         //     Description: null,
//         //     Duration: null,
//         //     InsuredName: null,
//         //     OwnerName: null,
//         //     PaymentMethod: null,
//         //     PlanType: null,
//         //     PolicyDate: null,
//         //     RenewalDate: null,
//         //     PolicyStatus: null,
//         //     PolicyStatusText: null,
//         //     ModifiedDate: null,
//         //     PolicyType: null,
//         //     Premium: null,
//         //     PrimaryAgentName: null,
//         //     PremiumMode: null,
//         //     ProductID: null,
//         //     ProductIDVal: null,
//         //     IssuedDate: null,
//         //     PrimaryInterestParty: {
//         //       InterestParty: {
//         //         ContactID: null,
//         //         Contact: {
//         //           Name: null,
//         //         },
//         //       },
//         //     },
//         //     InsuranceSyncUser: null,
//         //     PrimaryInsuredParty: {
//         //       InterestParty: {
//         //         ContactID: null,
//         //         Contact: {
//         //           Name: null,
//         //         },
//         //       },
//         //     },
//         //     Riders: {
//         //       Rider: {
//         //         Contact: {
//         //           Name: null,
//         //         },
//         //       },
//         //     },
//         //     PrimaryAdvisorParty: {
//         //       InterestParty: {
//         //         ContactID: null,
//         //         Contact: {
//         //           Name: null,
//         //         },
//         //       },
//         //     },
//         //     WriteState: null,
//         //     // The following object will be retrieved in subsequent get request
//         //     PortfolioName: null,
//         //     Contact: null,
//         //     PrimaryAdvisor: null,
//         //     MktManager: null,
//         //     Product: null,
//         //   },
//         //   condition: {
//         //     expr: {
//         //       '@prop': 'PolicyNumber',
//         //       '@op': Operation.EQUAL,
//         //       v: 'CI60030236',
//         //     },
//         //   },
//         // });
//         const policies = await prismaClient.report_data.findMany({
//           where: {
//             account_id: accountId,
//             state: {
//               in: [DataStates.ACTIVE, DataStates.GROUPED, DataStates.DUPLICATE],
//             },
//           },
//           select: {
//             id: true,
//             policy_id: true,
//             contacts: true,
//             contacts_split: true,
//             agent_payout_rate_override: true,
//             transaction_type: true,
//             sync_id: true,
//           },
//         });
//         // Group policies by policy_id to find duplicates
//         const policyMap = new Map<string, any[]>();

//         policies.forEach((policy) => {
//           if (policy.policy_id) {
//             if (!policyMap.has(policy.policy_id)) {
//               policyMap.set(policy.policy_id, []);
//             }
//             policyMap.get(policy.policy_id).push(policy);
//           }
//         });

//         // Filter for policy_ids with multiple policies (duplicates)
//         const duplicatePolicies = new Map<string, any[]>();

//         for (const [policy_id, policies] of policyMap.entries()) {
//           if (
//             policies.length > 1 &&
//             policies.some(
//               (r) =>
//                 r.sync_id && policies.some((r) => r.agent_payout_rate_override)
//             )
//           ) {
//             duplicatePolicies.set(policy_id, policies);
//             console.log(
//               `Found ${policies.length} duplicates for policy ${policy_id}`
//             );
//           }
//         }
//         let count = 0;

//         for (const [policy_id, policies] of duplicatePolicies.entries()) {
//           console.log(count++);
//           const synced = policies.find((p) => p.sync_id);
//           const manual = policies.find((p) => !p.sync_id);
//           console.log(manual.policy_id, synced.policy_id);
//           if (synced && manual) {
//             await prismaClient.report_data.updateMany({
//               where: {
//                 policy_id: policy_id,
//                 account_id: accountId,
//                 id: { not: manual.id },
//               },
//               data: {
//                 agent_payout_rate_override: manual.agent_payout_rate_override,
//                 contacts_split: manual.contacts_split,
//                 contacts: manual.contacts,
//                 transaction_type: manual.transaction_type,
//                 config: { overrideFields: ['contacts', 'contacts_split'] },
//               },
//             });
//           }
//         }

//         console.log(
//           `Found ${duplicatePolicies.size} policy IDs with duplicates out of ${policyMap.size} total policy IDs`
//         );
//         return true;
//       }
//     );
//   }

//   async test() {
//     const axios = require('axios');
//     const data = JSON.stringify({
//       productID: ********,
//       accountID: 4515439,
//       name: 'Medicare Part D Plan',
//       carrierID: 34071,
//       effectiveAsOf: '2022-01-01T08:00:00.000+00:00',
//       continuousPolicy: true,
//       unionProduct: false,
//       nonPayable: false,
//       nonRevenue: false,
//       numberOfEligibleEmployees: 1,
//       billingCarrierID: 34071,
//       officeID: 33218,
//       departmentID: 44410,
//       primarySalesLeadUserID: 255146,
//       primaryServiceLeadUserID: 255146,
//     });

//     const config = {
//       method: 'put',
//       maxBodyLength: Infinity,
//       url: 'https://preview2-api.uat.benefitpoint.vertafore.com/rest/V4_4/products/update/********',
//       headers: {
//         'Content-Type': 'application/json',
//         Accept: 'application/json',
//         sessionId: 'F0D985D900D6999045199CAC082D2A47',
//         Cookie:
//           'AWSALB=JnjuXsE8hKdvQI/U+ZZwhYjA0SXHyGXOoB9+geULW2DJ3WTNDOCyi/27X+91ir/5G6Epc8bblSsW1bFAvDUgpgnjsdQRbd1WYjg1UMpfFP+MvrKzEBFUGCWM6/cd; AWSALBAPP-0=_remove_; AWSALBAPP-1=_remove_; AWSALBAPP-2=_remove_; AWSALBAPP-3=_remove_; AWSALBCORS=JnjuXsE8hKdvQI/U+ZZwhYjA0SXHyGXOoB9+geULW2DJ3WTNDOCyi/27X+91ir/5G6Epc8bblSsW1bFAvDUgpgnjsdQRbd1WYjg1UMpfFP+MvrKzEBFUGCWM6/cd',
//       },
//       data: data,
//     };

//     return await axios
//       .request(config)
//       .then((response) => {
//         console.log(JSON.stringify(response.data));
//       })
//       .catch((error) => {
//         console.log(error);
//       });
//   }

//   @Get()
//   async testdm() {
//     const accountId = AccountIds.RISK_TAG;
//     return asyncLocalStorage.run(
//       {
//         traceId: '123',
//         account: {
//           account_id: accountId,
//           uid: '123',
//           ouid: '123',
//           role_id: '123',
//         },
//       },
//       async () => {
//         const groupDedupeService =
//           container.get<BenefitPointWorker>(BenefitPointWorker);
//         await groupDedupeService.setup({
//           account_id: accountId,
//         } as any);
//         const data =
//           await groupDedupeService.benefitPointService.auditLog(********);
//         console.log(data);
//         const service = new MigrationService();
//         await service.restoreBenefitPointData();
//         return true;
//       }
//     );
//   }
// }

// export default withLogger(createHandler(Test));
