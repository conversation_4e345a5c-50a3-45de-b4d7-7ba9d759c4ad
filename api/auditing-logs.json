[{"productID": 25818298, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Enrolled Employees/Enrolled Employees removed '10'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Number of Eligible Employees changed from '0' to '4'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Cancellation Reason removed"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Cancellation Additional Information removed"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Policy/Group Number changed from '104047401000' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24617100' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/05/2025 11:38:01 AM", "comment": "Enrolled Employees/Enrolled Employees added '10'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/05/2025 11:38:01 AM", "comment": "Cancellation Reason set to 'Other'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/05/2025 11:38:01 AM", "comment": "Cancellation Additional Information set to 'BOR'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/05/2025 11:38:01 AM", "comment": "Cancellation Date set to '06/01/2025'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/16/2024 05:38:30 AM", "comment": "Added GHP - All-Access PPO 40/90/8400 - Cobra renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/16/2024 05:38:30 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 26490849, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/09/2025 05:06:26 AM", "comment": "Edited HM - Blue Edge Dental Flex 3W w Ortho"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/09/2025 05:02:20 AM", "comment": "Automatic Activity Log Creation for Renewal changed from 'None Selected' to '90 days'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/09/2025 05:02:20 AM", "comment": "Prior Plan changed from 'None Selected' to 'BP Internal Plan/Product ID: ********'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/09/2025 04:35:36 AM", "comment": "Reinstatement Reason set to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/09/2025 04:35:36 AM", "comment": "Commissions/Fees Paid By Type changed from 'None Selected' to 'Insurer/TPA'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/09/2025 04:35:36 AM", "comment": "Billing Type changed from 'None Selected' to 'Direct Bill'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/09/2025 04:35:36 AM", "comment": "Origination Reason changed from 'None Selected' to 'Renewal'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/09/2025 04:35:36 AM", "comment": "Policy/Group Number changed from '' to '********'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:32:30 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '90 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:32:30 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:32:30 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:32:30 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:32:30 PM", "comment": "Policy/Group Number changed from '********' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:32:30 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "02/25/2025 06:14:55 AM", "comment": "Account Primary Service Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "10/18/2024 05:58:50 AM", "comment": "Added HM - Blue Edge Dental Flex 3W w Ortho renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "10/18/2024 05:58:50 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Enrolled Employees/Enrolled Employees removed '52'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '90 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Number of Eligible Employees changed from '0' to '14'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Policy/Group Number changed from '********' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/17/2025 10:42:05 PM", "comment": "Enrolled Employees/Enrolled Employees added '52'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/06/2025 08:57:22 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/25/2024 01:13:35 PM", "comment": "Added HM - Custom PPO Blue Sharing $2000 - Management renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/25/2024 01:13:35 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Automatic Activity Log Creation for Renewal changed from '90 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Number of Eligible Employees changed from '75' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Policy/Group Number changed from '********' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/06/2025 08:57:22 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/25/2024 01:34:32 PM", "comment": "Added Guardian VSP 61 Vision renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/25/2024 01:34:32 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:49 AM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:49 AM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:49 AM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:49 AM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:49 AM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:49 AM", "comment": "Policy/Group Number changed from '11227' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:49 AM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:49 AM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24329595' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "08/14/2024 09:16:19 AM", "comment": "Added Group Life with AD&D renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "08/14/2024 09:16:19 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 24361752, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:05 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '90 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:05 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:05 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:05 PM", "comment": "Origination Reason changed from 'Replacement' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:05 PM", "comment": "Policy/Group Number changed from '********' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:05 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:05 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/06/2025 08:57:22 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/22/2024 09:35:06 AM", "comment": "Renewal Date changed from '10/01/2024' to '10/01/2025'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "01/19/2024 05:42:53 AM", "comment": "Policy/Group Number changed from 'See' to '********'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "10/23/2023 12:51:23 PM", "comment": "Policy/Group Number changed from 'See Activities' to '********'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "10/23/2023 12:51:23 PM", "comment": "Plan Name changed from 'HM - Blue Edge Dental Flex 3W' to 'HM - Blue Edge Dental Flex 3W - Trucking'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "10/03/2023 11:07:24 AM", "comment": "Added HM - Blue Edge Dental Flex 3W replacement Plan"}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:20:10 PM", "comment": "Enrolled Employees/Enrolled Employees removed '10'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:20:10 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:20:10 PM", "comment": "Number of Eligible Employees changed from '0' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:20:10 PM", "comment": "Cancellation Reason removed"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:20:10 PM", "comment": "Cancellation Additional Information removed"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:20:10 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:20:10 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:20:10 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:20:10 PM", "comment": "Policy/Group Number changed from 'RSL004719700203801BCS004719' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:20:10 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 25556015' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/05/2025 11:42:48 AM", "comment": "Enrolled Employees/Enrolled Employees added '10'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/05/2025 11:42:48 AM", "comment": "Cancellation Reason set to 'Other'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/05/2025 11:42:48 AM", "comment": "Cancellation Additional Information set to 'BOR'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/05/2025 11:42:48 AM", "comment": "Cancellation Date set to '06/01/2025'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/05/2025 11:42:20 AM", "comment": "Added Short Term Disability renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/05/2025 11:42:20 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 26093757, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:48 AM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:48 AM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:48 AM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:48 AM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:48 AM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:48 AM", "comment": "Policy/Group Number changed from '********' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:48 AM", "comment": "Commission Start Date changed from '09/01/2021' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:48 AM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/06/2025 08:56:29 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/22/2024 09:37:54 AM", "comment": "Added Guardian - <PERSON><PERSON><PERSON> renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/22/2024 09:37:54 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Edited ER LTD - Class 2"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:26:30 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:26:30 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:26:30 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:26:30 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:26:30 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:26:30 PM", "comment": "Policy/Group Number changed from '451893' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:26:30 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:26:30 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "09/12/2024 12:11:08 PM", "comment": "Account Primary Service Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/18/2024 12:07:43 PM", "comment": "Added ER LTD - Class 2 renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/18/2024 12:07:43 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:14 AM", "comment": "Enrolled Employees/Enrolled Employees removed '2'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:14 AM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:14 AM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:14 AM", "comment": "Cancellation Reason removed"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:14 AM", "comment": "Cancellation Additional Information removed"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:14 AM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:14 AM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:14 AM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:14 AM", "comment": "Policy/Group Number changed from '00797797' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:14 AM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:14 AM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24827530' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/12/2025 08:27:54 AM", "comment": "Cancellation Reason set to 'Other'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/12/2025 08:27:54 AM", "comment": "Cancellation Additional Information set to 'Entered under incorrect group.'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/12/2025 08:27:54 AM", "comment": "Cancellation Date set to '01/01/2025'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "04/30/2025 08:53:16 AM", "comment": "Enrolled Employees/Enrolled Employees added '2'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "04/30/2025 08:53:16 AM", "comment": "Commissions/Fees Paid By changed from 'Highmark, Inc.' to 'Guardian'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "04/30/2025 08:53:16 AM", "comment": "Policy/Group Number changed from '10210645' to '00797797'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "04/30/2025 08:53:16 AM", "comment": "Carrier/Vendor changed from 'Highmark, Inc.' to 'Guardian'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "04/30/2025 08:53:16 AM", "comment": "Plan Name changed from 'HM- Vision Fashion Adv Opt I' to 'Guardian - Davis Vision 228'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "12/05/2024 12:44:45 PM", "comment": "Added HM- Vision Fashion Adv Opt I renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "12/05/2024 12:44:45 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 24037250, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:26 AM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:26 AM", "comment": "Reinstatement Additional Information removed"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:26 AM", "comment": "Reinstatement Reason removed"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:26 AM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:26 AM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:26 AM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:26 AM", "comment": "Policy/Group Number changed from '367956' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:26 AM", "comment": "Commission Start Date changed from '04/01/2002' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/06/2025 09:32:35 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "<EMAIL>", "csrName": null, "date": "05/22/2025 08:00:45 AM", "comment": "Account Primary Service Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "<EMAIL>", "csrName": null, "date": "05/22/2025 08:00:45 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "<EMAIL>", "csrName": null, "date": "05/20/2025 08:29:16 AM", "comment": "Account Primary Service Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "<EMAIL>", "csrName": null, "date": "05/20/2025 08:29:16 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "<EMAIL>", "csrName": null, "date": "09/10/2024 10:13:10 AM", "comment": "Primary Service Lead changed from '<EMAIL>' to '<EMAIL>'"}, {"userName": "<EMAIL>", "csrName": null, "date": "08/27/2024 12:39:28 PM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "<EMAIL>", "csrName": null, "date": "04/26/2024 10:11:51 AM", "comment": "Origination Reason changed from 'New' to 'Renewal'"}, {"userName": "<EMAIL>", "csrName": null, "date": "11/14/2023 10:15:12 AM", "comment": "Continuous Policy changed from 'unchecked' to 'checked'"}, {"userName": "<EMAIL>", "csrName": null, "date": "11/14/2023 10:15:12 AM", "comment": "Reinstatement Additional Information set to 'Reactivating to allocate for commissions we are receiving. '"}, {"userName": "<EMAIL>", "csrName": null, "date": "11/14/2023 10:15:12 AM", "comment": "Reinstatement Date set to '11/14/2023'"}, {"userName": "<EMAIL>", "csrName": null, "date": "11/14/2023 10:15:12 AM", "comment": "Reinstatement Reason set to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "11/14/2023 10:15:12 AM", "comment": "Cancellation Reason removed"}, {"userName": "<EMAIL>", "csrName": null, "date": "11/14/2023 10:15:12 AM", "comment": "Cancellation Date removed"}, {"userName": "<EMAIL>", "csrName": null, "date": "11/14/2023 10:15:12 AM", "comment": "Renewal Date changed from '04/01/2023' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "11/14/2023 09:58:37 AM", "comment": "Continuous Policy changed from 'checked' to 'unchecked'"}, {"userName": "<EMAIL>", "csrName": null, "date": "11/14/2023 09:58:37 AM", "comment": "Renewal Date changed from 'None Selected' to '04/01/2023'"}, {"userName": "<EMAIL>", "csrName": null, "date": "11/14/2023 08:50:23 AM", "comment": "Primary Sales Lead changed from '<EMAIL>' to '<EMAIL>'"}, {"userName": "<EMAIL>", "csrName": null, "date": "11/14/2023 08:50:23 AM", "comment": "Continuous Policy changed from 'unchecked' to 'checked'"}, {"userName": "<EMAIL>", "csrName": null, "date": "11/14/2023 08:50:23 AM", "comment": "Renewal Date changed from '04/01/2016' to 'None Selected'"}]}, {"productID": 26116037, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:58 AM", "comment": "Automatic Activity Log Creation for Renewal changed from '90 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:58 AM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:58 AM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:58 AM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:58 AM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:58 AM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24361775' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:58 AM", "comment": "Estimated Monthly Premium changed from '0.00' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:58 AM", "comment": "Broker of Record Date changed from '09/01/2012' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:58 AM", "comment": "Estimated Commission/Fee Period changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:58 AM", "comment": "Estimated Commission/Fee changed from '0.00' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:58 AM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:58 AM", "comment": "Policy/Group Number changed from '********' to ''"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/06/2025 08:57:23 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/25/2024 01:36:16 PM", "comment": "Added Voluntary Life - no AD&D renewal Additional Product"}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:49 AM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:49 AM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:49 AM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:49 AM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:49 AM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:49 AM", "comment": "Policy/Group Number changed from '11227' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:49 AM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:49 AM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24329596' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "08/14/2024 09:16:30 AM", "comment": "Added Group Short Term Disability renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "08/14/2024 09:16:30 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 26285317, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Enrolled Employees/Enrolled Employees removed '2'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Policy/Group Number changed from '518350' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/06/2025 09:30:11 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/12/2025 09:13:46 AM", "comment": "Enrolled Employees/Enrolled Employees added '2'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "09/05/2024 09:04:33 AM", "comment": "Added Dental renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "09/05/2024 09:04:33 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Enrolled Employees/Enrolled Employees removed '2'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Number of Eligible Employees changed from '3' to '5'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Policy/Group Number changed from '101154941000' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/17/2025 08:07:39 PM", "comment": "Enrolled Employees/Enrolled Employees added '2'"}, {"userName": "<EMAIL>", "csrName": null, "date": "09/12/2024 12:00:05 PM", "comment": "Account Primary Service Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "08/22/2024 09:31:27 AM", "comment": "Plan Name changed from 'GHP-PPO 500/1500 2X 20/40 20-35-60' to 'GHP-KYP All Access PPO 1000-2000 3x 20-40'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "08/22/2024 09:30:31 AM", "comment": "Added GHP-PPO 500/1500 2X 20/40 20-35-60 renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "08/22/2024 09:30:31 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:52 AM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:52 AM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:52 AM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:52 AM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:52 AM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:52 AM", "comment": "Policy/Group Number changed from '********' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:52 AM", "comment": "Commission Start Date changed from '09/01/2021' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:52 AM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/06/2025 08:56:29 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/22/2024 09:38:17 AM", "comment": "Added Guardian - VSP Choice Full Feature renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/22/2024 09:38:17 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:07 PM", "comment": "Enrolled Employees/Enrolled Employees removed '4'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:07 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:07 PM", "comment": "Number of Eligible Employees changed from '4' to '14'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:07 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:07 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:07 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:07 PM", "comment": "Policy/Group Number changed from '101059671000' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:07 PM", "comment": "Commission Start Date changed from '06/01/2024' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:07 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 26069859' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/17/2025 07:13:03 PM", "comment": "Enrolled Employees/Enrolled Employees added '4'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/11/2025 12:32:09 PM", "comment": "Plan was renewed through the Renew link"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/30/2024 05:47:10 AM", "comment": "Added GHP - All Access HMO $0 renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/30/2024 05:47:10 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 25821283, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:14:50 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:14:50 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:14:50 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:14:50 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:14:50 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:14:50 PM", "comment": "Policy/Group Number changed from '5384861' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:14:50 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:14:50 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24033392' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/23/2025 12:33:49 PM", "comment": "Plan was renewed through the Renew link"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/16/2024 01:06:24 PM", "comment": "Added MetLife - Basic Life AD&D renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/16/2024 01:06:24 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 26352555, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:31:23 PM", "comment": "Funding Type changed from 'Level Funded' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:31:23 PM", "comment": "Enrolled Employees/Enrolled Employees removed '25'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:31:23 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:31:23 PM", "comment": "Number of Eligible Employees changed from '0' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:31:23 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:31:23 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:31:23 PM", "comment": "Origination Reason changed from 'Replacement' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:31:23 PM", "comment": "Policy/Group Number changed from '0247603' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:31:23 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:31:23 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24329589' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/17/2025 07:42:34 PM", "comment": "Enrolled Employees/Enrolled Employees added '25'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "09/19/2024 09:20:57 AM", "comment": "Added Aetna - AFA COPSII 6750 100-50 IntRX PY V24 - Aetna Choice POS II replacement Plan"}]}, {"productID": 26192368, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:09 PM", "comment": "Enrolled Employees/Enrolled Employees removed '13'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:09 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:09 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:09 PM", "comment": "Number of Eligible Employees changed from '9' to '25'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:09 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:09 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:09 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:09 PM", "comment": "Policy/Group Number changed from '********' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:09 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:09 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/17/2025 08:16:36 PM", "comment": "Enrolled Employees/Enrolled Employees added '13'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "09/13/2024 04:44:23 AM", "comment": "Plan Name changed from 'GHP- ACA All Access PPO 20-40-500 Gold w/ HRA' to 'GHP- ACA All Access PPO 20-40-500 Gold w/ 2 HRAs'"}, {"userName": "<EMAIL>", "csrName": null, "date": "09/12/2024 12:03:11 PM", "comment": "Account Primary Service Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "08/15/2024 11:38:29 AM", "comment": "Policy/Group Number changed from '1011' to '********'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "08/12/2024 10:24:09 AM", "comment": "Policy/Group Number changed from '************' to '1011'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "08/12/2024 10:24:09 AM", "comment": "Plan Name changed from 'GHP- PPO w/ Ref $500/$1000 w HRA' to 'GHP- ACA All Access PPO 20-40-500 Gold w/ HRA'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "08/12/2024 10:23:24 AM", "comment": "Added GHP- PPO w/ Ref $500/$1000 w HRA renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "08/12/2024 10:23:24 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Enrolled Employees/Enrolled Employees removed '15'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Number of Eligible Employees changed from '12' to '5'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Policy/Group Number changed from '10622547' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/17/2025 08:29:36 PM", "comment": "Enrolled Employees/Enrolled Employees added '15'"}, {"userName": "<EMAIL>", "csrName": null, "date": "09/12/2024 12:05:37 PM", "comment": "Account Primary Service Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/27/2024 08:52:28 AM", "comment": "Added HM - PPO Blue $6000 2x renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/27/2024 08:52:28 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:12 PM", "comment": "Funding Type changed from 'Level Funded' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:12 PM", "comment": "Enrolled Employees/Enrolled Employees removed '12'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:12 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:12 PM", "comment": "Number of Eligible Employees changed from '26' to '12'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:12 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:12 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:12 PM", "comment": "Origination Reason changed from 'Replacement' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:12 PM", "comment": "Policy/Group Number changed from '1684810' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:12 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/17/2025 07:51:36 PM", "comment": "Enrolled Employees/Enrolled Employees added '12'"}, {"userName": "<EMAIL>", "csrName": null, "date": "08/26/2024 08:20:21 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "<EMAIL>", "csrName": null, "date": "08/13/2024 12:15:49 PM", "comment": "Account Primary Service Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/26/2024 08:31:51 AM", "comment": "Added UHC - E1000i100LXES21B Choice EPO $1000  replacement Plan"}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Enrolled Employees/Enrolled Employees removed '52'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '90 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Number of Eligible Employees changed from '0' to '2'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Policy/Group Number changed from '********' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/17/2025 10:41:53 PM", "comment": "Enrolled Employees/Enrolled Employees added '52'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/06/2025 08:57:22 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/25/2024 01:13:25 PM", "comment": "Added HM - Custom PPO Blue Sharing $2000 - Appalachian Services renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/25/2024 01:13:25 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Enrolled Employees/Enrolled Employees removed '4'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Policy/Group Number changed from '********' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/17/2025 11:29:25 PM", "comment": "Enrolled Employees/Enrolled Employees added '4'"}, {"userName": "<EMAIL>", "csrName": null, "date": "02/06/2025 07:13:50 PM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "08/23/2024 10:17:22 AM", "comment": "Policy/Group Number changed from '1111' to '********'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "08/23/2024 10:17:22 AM", "comment": "Plan Name changed from 'HM - BlueCare Custom PPO $2600 Silver' to 'HM - BlueCare Custom PPO $1000 Gold'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "08/08/2024 01:04:26 PM", "comment": "Policy/Group Number changed from '********' to '1111'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "08/08/2024 01:04:26 PM", "comment": "Plan Name changed from 'HM - Custom PPO $1000 Gold' to 'HM - BlueCare Custom PPO $2600 Silver'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "08/08/2024 01:03:45 PM", "comment": "Added HM - Custom PPO $1000 Gold renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "08/08/2024 01:03:45 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 26069906, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:12 PM", "comment": "Funding Type changed from 'Level Funded' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:12 PM", "comment": "Enrolled Employees/Enrolled Employees removed '20'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:12 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '90 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:12 PM", "comment": "Number of Eligible Employees changed from '94' to '-17'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:12 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:12 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:12 PM", "comment": "Origination Reason changed from 'Replacement' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:12 PM", "comment": "Policy/Group Number changed from '1674534' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:12 PM", "comment": "Commission Start Date changed from '07/01/2024' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:12 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 26069878' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/17/2025 08:28:29 PM", "comment": "Enrolled Employees/Enrolled Employees added '20'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/28/2025 10:12:49 AM", "comment": "Plan was renewed through the Renew link"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/17/2024 09:25:07 AM", "comment": "Commission Start Date changed from 'None Selected' to '07/01/2024'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/15/2024 05:46:01 AM", "comment": "Added UHC - Choice E3000i100LXES21B EPO $3000  replacement Plan"}]}, {"productID": 24033144, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:21:35 PM", "comment": "Enrolled Employees/Enrolled Employees removed '8'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:21:35 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:21:35 PM", "comment": "Reinstatement Reason removed"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:21:35 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:21:35 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:21:35 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:21:35 PM", "comment": "Policy/Group Number changed from '243907' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:21:35 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:21:35 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/09/2025 08:48:37 AM", "comment": "Enrolled Employees/Enrolled Employees added '8'"}, {"userName": "<EMAIL>", "csrName": null, "date": "02/06/2025 07:53:04 PM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "<EMAIL>", "csrName": null, "date": "12/05/2024 01:55:28 PM", "comment": "Continuous Policy changed from 'unchecked' to 'checked'"}, {"userName": "<EMAIL>", "csrName": null, "date": "12/05/2024 01:55:28 PM", "comment": "Renewal Date changed from '03/01/2025' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "12/05/2024 01:55:10 PM", "comment": "Reinstatement Date set to '02/01/2024'"}, {"userName": "<EMAIL>", "csrName": null, "date": "12/05/2024 01:55:10 PM", "comment": "Reinstatement Reason set to 'Error in Company Cancellation'"}, {"userName": "<EMAIL>", "csrName": null, "date": "12/05/2024 01:55:10 PM", "comment": "Cancellation Reason removed"}, {"userName": "<EMAIL>", "csrName": null, "date": "12/05/2024 01:55:10 PM", "comment": "Cancellation Date removed"}, {"userName": "<EMAIL>", "csrName": null, "date": "11/13/2024 07:17:01 AM", "comment": "Cancellation Reason set to 'Discontinued Line of Coverage'"}, {"userName": "<EMAIL>", "csrName": null, "date": "11/13/2024 07:17:01 AM", "comment": "Cancellation Date set to '02/01/2024'"}, {"userName": "<EMAIL>", "csrName": null, "date": "08/06/2024 07:43:24 AM", "comment": "Account Primary Service Lead automatically <NAME_EMAIL> to <EMAIL>"}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Number of Eligible Employees changed from 'None Selected' to '43'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Policy/Group Number changed from '117561910001' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Commission Start Date changed from '06/01/2023' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24033400' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/23/2025 12:33:38 PM", "comment": "Plan was renewed through the Renew link"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/16/2024 01:06:38 PM", "comment": "Added Principal - Voluntary Term Life renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/16/2024 01:06:38 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 25821282, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Number of Eligible Employees changed from 'None Selected' to '6'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Policy/Group Number changed from '117561910001' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Commission Start Date changed from '06/01/2023' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24033399' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/23/2025 12:34:02 PM", "comment": "Plan was renewed through the Renew link"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/16/2024 01:06:10 PM", "comment": "Added Principal - VSP Choice renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/16/2024 01:06:10 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 26116034, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:01 AM", "comment": "Automatic Activity Log Creation for Renewal changed from '90 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:01 AM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:01 AM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:01 AM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:01 AM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:01 AM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24361773' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:01 AM", "comment": "Estimated Monthly Premium changed from '0.00' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:01 AM", "comment": "Broker of Record Date changed from '09/01/2014' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:01 AM", "comment": "Estimated Commission/Fee Period changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:01 AM", "comment": "Estimated Commission/Fee changed from '0.00' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:01 AM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:01 AM", "comment": "Policy/Group Number changed from '********' to ''"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/06/2025 08:57:23 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/25/2024 01:36:05 PM", "comment": "Added Voluntary Critical Illness renewal Additional Product"}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '90 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Number of Eligible Employees changed from 'None Selected' to '0'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Origination Reason changed from 'Replacement' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Policy/Group Number changed from '********' to ''"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/06/2025 08:57:22 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/22/2024 09:35:20 AM", "comment": "Renewal Date changed from '10/01/2024' to '10/01/2025'"}, {"userName": "<EMAIL>", "csrName": null, "date": "04/26/2024 09:38:14 AM", "comment": "Origination Reason changed from 'New' to 'Replacement'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "01/19/2024 05:40:33 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:33 PM", "comment": "Enrolled Employees/Enrolled Employees removed '25'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:33 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:33 PM", "comment": "Number of Eligible Employees changed from 'None Selected' to '20'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:33 PM", "comment": "Cancellation Reason removed"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:33 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:33 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:33 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:33 PM", "comment": "Policy/Group Number changed from '10232999' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:33 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:33 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 25022847' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/17/2025 07:36:12 PM", "comment": "Enrolled Employees/Enrolled Employees changed from '30' to '25'"}, {"userName": "<EMAIL>", "csrName": null, "date": "05/27/2025 08:03:28 AM", "comment": "Enrolled Employees/Enrolled Employees added '30'"}, {"userName": "<EMAIL>", "csrName": null, "date": "05/27/2025 08:03:28 AM", "comment": "Cancellation Reason set to 'Lost Broker of Record for this Product'"}, {"userName": "<EMAIL>", "csrName": null, "date": "05/27/2025 08:03:28 AM", "comment": "Cancellation Date set to '07/01/2025'"}, {"userName": "<EMAIL>", "csrName": null, "date": "12/05/2024 01:39:06 PM", "comment": "Added HM- BC PPO 2500 0% 3/20/40/60 w HRA renewal Plan"}, {"userName": "<EMAIL>", "csrName": null, "date": "12/05/2024 01:39:06 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 26086413, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Edited VSP 59"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:26:36 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:26:36 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:26:36 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:26:36 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:26:36 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:26:36 PM", "comment": "Policy/Group Number changed from '451893' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:26:36 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:26:36 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "09/12/2024 12:11:08 PM", "comment": "Account Primary Service Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/18/2024 12:07:32 PM", "comment": "Added VSP 59 renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/18/2024 12:07:32 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Policy/Group Number changed from '792794' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24033406' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/15/2025 09:35:00 AM", "comment": "Plan was renewed through the Renew link"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/06/2024 12:06:32 PM", "comment": "Added STD renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/06/2024 12:06:32 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 25764440, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:03 AM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:03 AM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:03 AM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:03 AM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:03 AM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:03 AM", "comment": "Policy/Group Number changed from '792794' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:03 AM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:03 AM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24033404' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/15/2025 09:34:50 AM", "comment": "Plan was renewed through the Renew link"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/06/2024 12:06:21 PM", "comment": "Added Basic Life - ER paid $20,000 renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/06/2024 12:06:20 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 25821276, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:31:22 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:31:22 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:31:22 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:31:22 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:31:22 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:31:22 PM", "comment": "Policy/Group Number changed from '197658' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:31:22 PM", "comment": "Commission Start Date changed from '07/01/2023' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:31:22 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24033396' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/23/2025 12:41:02 PM", "comment": "Plan was replaced through the Replace link"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/16/2024 01:05:20 PM", "comment": "Added Aetna - AFA CPOSII 2500 100 50 PY renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/16/2024 01:05:20 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 25818303, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:09 PM", "comment": "Enrolled Employees/Enrolled Employees removed '10'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:09 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:09 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:09 PM", "comment": "Number of Eligible Employees changed from 'None Selected' to '9'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:09 PM", "comment": "Cancellation Reason removed"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:09 PM", "comment": "Cancellation Additional Information removed"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:09 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:09 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:09 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:09 PM", "comment": "Policy/Group Number changed from '104047411000' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:09 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:09 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24033349' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/05/2025 11:35:36 AM", "comment": "Cancellation Reason set to 'Other'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/05/2025 11:35:36 AM", "comment": "Cancellation Additional Information set to 'BOR'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/05/2025 11:35:36 AM", "comment": "Cancellation Date set to '06/01/2025'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/05/2025 11:35:00 AM", "comment": "Renewal Date changed from '06/01/2026' to '06/01/2025'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/05/2025 11:34:10 AM", "comment": "Renewal Date changed from '05/31/2025' to '06/01/2026'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/05/2025 11:30:05 AM", "comment": "Enrolled Employees/Enrolled Employees added '10'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/05/2025 11:30:05 AM", "comment": "Renewal Date changed from '06/01/2025' to '05/31/2025'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/16/2024 05:43:10 AM", "comment": "Added All-Access PPO 40/90/8400 renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/16/2024 05:43:10 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 25992265, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Enrolled Employees/Enrolled Employees removed '15'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Converted Fields/Employee Termination removed 'End of month following termination'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Number of Eligible Employees changed from '1' to '4'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Policy/Group Number changed from '10574297' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/17/2025 08:29:26 PM", "comment": "Enrolled Employees/Enrolled Employees added '15'"}, {"userName": "<EMAIL>", "csrName": null, "date": "09/12/2024 12:05:37 PM", "comment": "Account Primary Service Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/27/2024 08:52:16 AM", "comment": "Added HM - PPO Blue $500 renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/27/2024 08:52:16 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Enrolled Employees/Enrolled Employees removed '52'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '90 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Number of Eligible Employees changed from '0' to '39'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Policy/Group Number changed from '********' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/17/2025 10:42:35 PM", "comment": "Enrolled Employees/Enrolled Employees added '52'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/06/2025 08:57:22 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/25/2024 01:33:53 PM", "comment": "Added HM - PPO Blue Sharing $2000 - Management renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/25/2024 01:33:53 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:06 PM", "comment": "Enrolled Employees/Enrolled Employees removed '3'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:06 PM", "comment": "Number of Eligible Employees changed from '12' to '18'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:06 PM", "comment": "Cancellation Reason removed"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:06 PM", "comment": "Cancellation Additional Information removed"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:06 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:06 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:06 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:06 PM", "comment": "Policy/Group Number changed from '104041561000' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:06 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:06 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24051049' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "05/13/2025 08:04:35 AM", "comment": "Enrolled Employees/Enrolled Employees added '3'"}, {"userName": "<EMAIL>", "csrName": null, "date": "05/13/2025 08:04:35 AM", "comment": "Cancellation Reason set to 'Replaced Product'"}, {"userName": "<EMAIL>", "csrName": null, "date": "05/13/2025 08:04:35 AM", "comment": "Cancellation Additional Information set to 'Group is no longer offering this plan'"}, {"userName": "<EMAIL>", "csrName": null, "date": "05/13/2025 08:04:35 AM", "comment": "Cancellation Date set to '06/01/2025'"}, {"userName": "<EMAIL>", "csrName": null, "date": "04/05/2024 07:36:05 AM", "comment": "Added GHP PPO 10/20/0 Platinum - HRA renewal Plan"}, {"userName": "<EMAIL>", "csrName": null, "date": "04/05/2024 07:36:05 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 25764437, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Policy/Group Number changed from '792794' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24033407' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/15/2025 09:34:38 AM", "comment": "Plan was renewed through the Renew link"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/06/2024 12:06:09 PM", "comment": "Added Davis Vision renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/06/2024 12:06:09 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 25764453, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24033408' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Estimated Monthly Premium changed from '0.00' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Broker of Record Date changed from '07/01/2019' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Estimated Commission/Fee Period changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Estimated Commission/Fee changed from '0.00' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Policy/Group Number changed from '792794' to ''"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/15/2025 09:35:24 AM", "comment": "Additional Product was renewed through the Renew link"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/06/2024 12:06:58 PM", "comment": "Added Voluntary Life renewal Additional Product"}]}, {"productID": 25815819, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Enrolled Employees/Enrolled Employees removed '12'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Number of Eligible Employees changed from '7' to '16'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Policy/Group Number changed from '101186171002' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24033390' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/19/2025 08:52:26 AM", "comment": "Policy/Group Number changed from '101186171000' to '101186171002'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/18/2025 06:56:12 PM", "comment": "Enrolled Employees/Enrolled Employees added '12'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/06/2025 09:55:09 AM", "comment": "Plan was renewed through the Renew link"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/15/2024 12:48:45 PM", "comment": "Added GFA PPO All Access $1500/$3000 RX Plan A renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/15/2024 12:48:45 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 26192500, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Enrolled Employees/Enrolled Employees removed '2'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Number of Eligible Employees changed from '2' to '8'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Policy/Group Number changed from '101186811000' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/17/2025 06:31:40 PM", "comment": "Enrolled Employees/Enrolled Employees added '2'"}, {"userName": "<EMAIL>", "csrName": null, "date": "03/06/2025 06:49:40 AM", "comment": "Account Primary Service Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "<EMAIL>", "csrName": null, "date": "03/06/2025 06:49:40 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "<EMAIL>", "csrName": null, "date": "08/26/2024 08:15:57 AM", "comment": "Account Primary Sales Lead automatically changed from su<PERSON><PERSON>@benegroup.<NAME_EMAIL>"}, {"userName": "<EMAIL>", "csrName": null, "date": "08/26/2024 08:14:05 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "08/12/2024 10:51:06 AM", "comment": "Added GFA PPO 20/40/500 renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "08/12/2024 10:51:06 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Number of Eligible Employees changed from 'None Selected' to '5'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Policy/Group Number changed from '117561910001' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Commission Start Date changed from '06/01/2023' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 04:35:34 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24033398' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/23/2025 12:34:15 PM", "comment": "Plan was renewed through the Renew link"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/16/2024 01:05:58 PM", "comment": "Added Principal - Dental (Option 1 - High; Option 2 - Low) renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/16/2024 01:05:58 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 26829309, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:33 PM", "comment": "Enrolled Employees/Enrolled Employees removed '25'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:33 PM", "comment": "Number of Eligible Employees changed from '0' to '1'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:32 PM", "comment": "Cancellation Reason removed"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:32 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:32 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:32 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:32 PM", "comment": "Policy/Group Number changed from '1780849' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:32 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 25347213' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/17/2025 07:36:03 PM", "comment": "Enrolled Employees/Enrolled Employees changed from '30' to '25'"}, {"userName": "<EMAIL>", "csrName": null, "date": "05/27/2025 08:04:50 AM", "comment": "Enrolled Employees/Enrolled Employees added '30'"}, {"userName": "<EMAIL>", "csrName": null, "date": "05/27/2025 08:04:50 AM", "comment": "Cancellation Reason set to 'Lost Broker of Record for this Product'"}, {"userName": "<EMAIL>", "csrName": null, "date": "05/27/2025 08:04:50 AM", "comment": "Cancellation Date set to '07/01/2025'"}, {"userName": "<EMAIL>", "csrName": null, "date": "12/05/2024 01:39:28 PM", "comment": "Added BlueCare Custom PPO $2500 COBRA renewal Plan"}, {"userName": "<EMAIL>", "csrName": null, "date": "12/05/2024 01:39:28 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 26829307, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:32 PM", "comment": "Enrolled Employees/Enrolled Employees removed '30'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:32 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:32 PM", "comment": "Number of Eligible Employees changed from '28' to '0'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:32 PM", "comment": "Cancellation Reason removed"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:32 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:32 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:32 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:32 PM", "comment": "Policy/Group Number changed from '10210699' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:32 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:32 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 25022853' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "05/27/2025 08:04:05 AM", "comment": "Enrolled Employees/Enrolled Employees added '30'"}, {"userName": "<EMAIL>", "csrName": null, "date": "05/27/2025 08:04:05 AM", "comment": "Cancellation Reason set to 'Lost Broker of Record for this Product'"}, {"userName": "<EMAIL>", "csrName": null, "date": "05/27/2025 08:04:05 AM", "comment": "Cancellation Date set to '07/01/2025'"}, {"userName": "<EMAIL>", "csrName": null, "date": "12/05/2024 01:39:17 PM", "comment": "Added HM BC Vision- Fashion Advantage Opt 1 renewal Plan"}, {"userName": "<EMAIL>", "csrName": null, "date": "12/05/2024 01:39:17 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 25821288, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:14:50 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:14:50 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:14:50 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:14:50 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:14:50 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:14:50 PM", "comment": "Policy/Group Number changed from '5384861' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:14:50 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:14:50 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24033394' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/23/2025 12:33:16 PM", "comment": "Plan was renewed through the Renew link"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/16/2024 01:07:06 PM", "comment": "Added MetLife - LTD renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/16/2024 01:07:05 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 24471289, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '90 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Origination Reason changed from 'Replacement' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Policy/Group Number changed from '********' to ''"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/06/2025 08:57:22 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/22/2024 09:35:31 AM", "comment": "Renewal Date changed from '10/01/2024' to '10/01/2025'"}, {"userName": "<EMAIL>", "csrName": null, "date": "04/26/2024 09:38:01 AM", "comment": "Origination Reason changed from 'New' to 'Replacement'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "10/23/2023 12:59:11 PM", "comment": "Added All Employees"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "10/23/2023 12:59:11 PM", "comment": "Added HM - Blue Edge Dental Flex 3W - Management Plan"}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '90 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Origination Reason changed from 'Replacement' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Policy/Group Number changed from '********' to ''"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/06/2025 08:57:22 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/22/2024 09:35:43 AM", "comment": "Renewal Date changed from '10/01/2024' to '10/01/2025'"}, {"userName": "<EMAIL>", "csrName": null, "date": "04/26/2024 09:37:50 AM", "comment": "Origination Reason changed from 'New' to 'Replacement'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "10/23/2023 12:58:22 PM", "comment": "Added All Employees"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "10/23/2023 12:58:22 PM", "comment": "Added HM - Blue Edge Dental Flex 3W - Appalachian Services Plan"}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Automatic Activity Log Creation for Renewal changed from '90 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Policy/Group Number changed from '********' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/06/2025 08:57:22 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/25/2024 01:35:10 PM", "comment": "Added STD renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/25/2024 01:35:10 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Enrolled Employees/Enrolled Employees removed '52'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '90 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Number of Eligible Employees changed from '68' to '1'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Policy/Group Number changed from '10812384' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:07 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/17/2025 10:42:14 PM", "comment": "Enrolled Employees/Enrolled Employees added '52'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/06/2025 08:57:22 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/25/2024 01:13:46 PM", "comment": "Added HM - Custom PPO Blue Sharing $2000 - Trucking renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/25/2024 01:13:46 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Automatic Activity Log Creation for Renewal changed from '90 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Policy/Group Number changed from '********' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:55 AM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/06/2025 08:57:22 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/25/2024 01:35:22 PM", "comment": "Added Voluntary LTD renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/25/2024 01:35:22 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Number of Eligible Employees changed from '3' to '2'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Policy/Group Number changed from '101166981000' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "04/30/2025 07:17:15 AM", "comment": "Plan was replaced through the Replace link"}, {"userName": "<EMAIL>", "csrName": null, "date": "09/12/2024 12:08:31 PM", "comment": "Account Primary Service Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/20/2024 08:49:16 AM", "comment": "Added GHP - KYP - All Access PPO $1250 renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/20/2024 08:49:16 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Policy/Group Number changed from '792794' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24033405' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/15/2025 09:35:13 AM", "comment": "Plan was renewed through the Renew link"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/06/2024 12:06:44 PM", "comment": "Added LTD renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/06/2024 12:06:44 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 25764432, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Policy/Group Number changed from '792794' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24033403' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/15/2025 09:34:28 AM", "comment": "Plan was renewed through the Renew link"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/06/2024 12:05:56 PM", "comment": "Added Dental PPO renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/06/2024 12:05:56 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 26148358, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/08/2025 07:33:16 AM", "comment": "Plan was renewed through the Renew link"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:18 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:18 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:18 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:18 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:18 PM", "comment": "Policy/Group Number changed from '************' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:18 PM", "comment": "Commission Start Date changed from '09/01/2023' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:18 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "09/12/2024 12:12:34 PM", "comment": "Account Primary Service Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "08/02/2024 04:10:39 AM", "comment": "Added GHP - Choices PPO 20/40/2000 renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "08/02/2024 04:10:39 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Enrolled Employees/Enrolled Employees removed '1'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Policy/Group Number changed from '104053811000' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/17/2025 08:06:38 PM", "comment": "Enrolled Employees/Enrolled Employees added '1'"}, {"userName": "<EMAIL>", "csrName": null, "date": "05/27/2025 07:06:54 AM", "comment": "Plan was renewed through the Renew link"}, {"userName": "<EMAIL>", "csrName": null, "date": "09/12/2024 11:59:20 AM", "comment": "Account Primary Service Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/20/2024 01:04:10 PM", "comment": "Added GHP - ACA Choices PPO 10/20/0 Platinum renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/20/2024 01:04:09 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Enrolled Employees/Enrolled Employees removed '15'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Converted Fields/Employee Termination removed 'End of month following termination'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Number of Eligible Employees changed from '30' to '8'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Policy/Group Number changed from '10574295' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/17/2025 08:29:16 PM", "comment": "Enrolled Employees/Enrolled Employees added '15'"}, {"userName": "<EMAIL>", "csrName": null, "date": "09/12/2024 12:05:37 PM", "comment": "Account Primary Service Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/27/2024 08:52:02 AM", "comment": "Added HM - BlueCare HMO $6000 renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/27/2024 08:52:02 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '90 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Number of Eligible Employees changed from 'None Selected' to '0'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Origination Reason changed from 'Replacement' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Policy/Group Number changed from '********' to ''"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/06/2025 08:57:22 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/22/2024 09:34:51 AM", "comment": "Renewal Date changed from '10/01/2024' to '10/01/2025'"}, {"userName": "<EMAIL>", "csrName": null, "date": "04/26/2024 09:38:32 AM", "comment": "Origination Reason changed from 'New' to 'Replacement'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "01/19/2024 05:42:20 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:08:43 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '90 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:08:43 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:08:43 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:08:43 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:08:43 PM", "comment": "Policy/Group Number changed from '********' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:08:43 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/06/2025 08:57:22 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "12/26/2024 01:16:19 PM", "comment": "Added Copy of HM - Blue Edge Dental Flex 3W - Appalachian Services - Cobra renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "12/26/2024 01:16:19 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/07/2025 12:47:52 PM", "comment": "Plan was renewed through the Renew link"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:24:42 PM", "comment": "Enrolled Employees/Enrolled Employees removed '26'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:24:42 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:24:42 PM", "comment": "Number of Eligible Employees changed from '0' to '9'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:24:42 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:24:42 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:24:42 PM", "comment": "Origination Reason changed from 'New' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:24:42 PM", "comment": "Policy/Group Number changed from '1673739' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/17/2025 11:19:18 PM", "comment": "Enrolled Employees/Enrolled Employees added '26'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "01/10/2025 12:59:22 PM", "comment": "Plan Name changed from 'UHC - EPO - E2500i100LXES21B $2500 ' to 'UHC - EPO - Choice E2500i100LXES21B $2500 '"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/30/2024 06:14:00 AM", "comment": "Policy/Group Number changed from '111111' to '1673739'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/17/2024 09:42:25 AM", "comment": "Account Primary Service Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/24/2024 09:10:54 AM", "comment": "Added All Employees"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/24/2024 09:10:54 AM", "comment": "Added UHC - EPO - E2500i100LXES21B $2500  Plan"}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Enrolled Employees/Enrolled Employees removed '2'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Policy/Group Number changed from '518350' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/06/2025 09:30:11 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/12/2025 09:13:55 AM", "comment": "Enrolled Employees/Enrolled Employees added '2'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "09/05/2024 09:04:20 AM", "comment": "Added VSP G 36 renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "09/05/2024 09:04:20 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:58 AM", "comment": "Automatic Activity Log Creation for Renewal changed from '90 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:58 AM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:58 AM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:58 AM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:58 AM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:58 AM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24361772' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:58 AM", "comment": "Estimated Monthly Premium changed from '0.00' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:58 AM", "comment": "Broker of Record Date changed from '09/01/2014' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:58 AM", "comment": "Estimated Commission/Fee Period changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:58 AM", "comment": "Estimated Commission/Fee changed from '0.00' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:58 AM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:58 AM", "comment": "Policy/Group Number changed from '********' to ''"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/06/2025 08:57:23 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/25/2024 01:35:54 PM", "comment": "Added Voluntary Accident renewal Additional Product"}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Number of Eligible Employees changed from '0' to '8'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Origination Reason changed from 'Replacement' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Policy/Group Number changed from '************' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:17 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "05/14/2025 09:12:41 AM", "comment": "Plan was replaced through the Replace link"}, {"userName": "<EMAIL>", "csrName": null, "date": "09/12/2024 12:09:33 PM", "comment": "Account Primary Service Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/30/2024 12:02:37 PM", "comment": "Policy/Group Number changed from '000000' to '************'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/16/2024 05:50:44 AM", "comment": "Added GHP - ACA All Access PPO 25-50-2000 replacement Plan"}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Enrolled Employees/Enrolled Employees removed '4'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Number of Eligible Employees changed from '10' to '2'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Policy/Group Number changed from '10616578' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:06 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/17/2025 08:04:37 PM", "comment": "Enrolled Employees/Enrolled Employees changed from '2' to '4'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/06/2025 09:30:11 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/12/2025 09:14:41 AM", "comment": "Enrolled Employees/Enrolled Employees changed from '3' to '2'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/02/2025 01:29:05 PM", "comment": "Enrolled Employees/Enrolled Employees changed from '1' to '3'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/02/2025 01:28:38 PM", "comment": "Enrolled Employees/Enrolled Employees added '1'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "09/05/2024 09:04:56 AM", "comment": "Added HM BlueCare PPO $1250 Gold renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "09/05/2024 09:04:56 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:48 AM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:48 AM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:48 AM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:48 AM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:48 AM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:48 AM", "comment": "Policy/Group Number changed from '15727' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:48 AM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:48 AM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24033360' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "04/02/2025 12:31:41 PM", "comment": "Plan was renewed through the Renew link"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "03/13/2024 07:56:20 AM", "comment": "Added Equitable - ER Paid Basic Life w/ AD&D renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "03/13/2024 07:56:20 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 25821286, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:14:50 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:14:50 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:14:50 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:14:50 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:14:50 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:14:50 PM", "comment": "Policy/Group Number changed from '5384861' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:14:50 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:14:50 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24033393' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/23/2025 12:33:27 PM", "comment": "Plan was renewed through the Renew link"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/16/2024 01:06:53 PM", "comment": "Added MetLife - STD renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/16/2024 01:06:53 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 25818298, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Enrolled Employees/Enrolled Employees removed '10'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Number of Eligible Employees changed from '0' to '4'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Cancellation Reason removed"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Cancellation Additional Information removed"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Policy/Group Number changed from '104047401000' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:07:08 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24617100' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/05/2025 11:38:01 AM", "comment": "Enrolled Employees/Enrolled Employees added '10'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/05/2025 11:38:01 AM", "comment": "Cancellation Reason set to 'Other'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/05/2025 11:38:01 AM", "comment": "Cancellation Additional Information set to 'BOR'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/05/2025 11:38:01 AM", "comment": "Cancellation Date set to '06/01/2025'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/16/2024 05:38:30 AM", "comment": "Added GHP - All-Access PPO 40/90/8400 - Cobra renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/16/2024 05:38:30 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 26829301, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:33 PM", "comment": "Enrolled Employees/Enrolled Employees removed '25'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:33 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:33 PM", "comment": "Number of Eligible Employees changed from 'None Selected' to '15'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:33 PM", "comment": "Cancellation Reason removed"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:33 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:33 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:33 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:33 PM", "comment": "Policy/Group Number changed from '10210700' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:33 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:10:33 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 25022850' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/17/2025 07:36:21 PM", "comment": "Enrolled Employees/Enrolled Employees changed from '30' to '25'"}, {"userName": "<EMAIL>", "csrName": null, "date": "05/27/2025 08:02:49 AM", "comment": "Cancellation Reason set to 'Lost Broker of Record for this Product'"}, {"userName": "<EMAIL>", "csrName": null, "date": "05/27/2025 08:02:49 AM", "comment": "Cancellation Date set to '07/01/2025'"}, {"userName": "<EMAIL>", "csrName": null, "date": "05/27/2025 08:01:42 AM", "comment": "Enrolled Employees/Enrolled Employees added '30'"}, {"userName": "<EMAIL>", "csrName": null, "date": "05/27/2025 08:01:41 AM", "comment": "Renewal Date changed from '01/01/2026' to '07/01/2025'"}, {"userName": "<EMAIL>", "csrName": null, "date": "12/05/2024 01:38:56 PM", "comment": "Added HM-BC Custom PPO 2500 0% 3/20/40/60 w HRA renewal Plan"}, {"userName": "<EMAIL>", "csrName": null, "date": "12/05/2024 01:38:55 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 26315032, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:13 PM", "comment": "Enrolled Employees/Enrolled Employees removed '2'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:13 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:13 PM", "comment": "Number of Eligible Employees changed from '0' to '2'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:13 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:13 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:13 PM", "comment": "Origination Reason changed from 'New' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 05:15:13 PM", "comment": "Policy/Group Number changed from '1678845' to ''"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/03/2025 12:59:42 PM", "comment": "Enrolled Employees/Enrolled Employees added '2'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "09/10/2024 08:55:15 AM", "comment": "Added All Employees"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "09/10/2024 08:55:15 AM", "comment": "Added UHC - Vision S104C Plan"}]}, {"productID": 26201281, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:48 AM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:48 AM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:48 AM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:48 AM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:48 AM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:48 AM", "comment": "Policy/Group Number changed from '11227' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:48 AM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:56:48 AM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24329598' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "08/14/2024 09:16:42 AM", "comment": "Added Group Long Term Disability renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "08/14/2024 09:16:42 AM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": 25955579, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Automatic Activity Log Creation for Renewal changed from '90 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Number of Eligible Employees changed from '0' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Policy/Group Number changed from '338611' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:53 AM", "comment": "Commission Start Date changed from '07/01/2024' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "05/28/2025 10:13:11 AM", "comment": "Plan was renewed through the Renew link"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/15/2024 05:28:12 AM", "comment": "Origination Reason changed from 'New' to 'Renewal'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/11/2024 09:11:53 AM", "comment": "Commission Start Date changed from 'None Selected' to '07/01/2024'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/18/2024 10:49:52 AM", "comment": "Added All Employees"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/18/2024 10:49:51 AM", "comment": "Added Dental PPO Plan"}]}, {"productID": 26116022, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:54 AM", "comment": "Automatic Activity Log Creation for Renewal changed from '90 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:54 AM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:54 AM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:54 AM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:54 AM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:54 AM", "comment": "Policy/Group Number changed from '********' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:54 AM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:00:54 AM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/06/2025 08:57:22 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/25/2024 01:34:44 PM", "comment": "Added Basic Life - AD&D renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/25/2024 01:34:44 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:08 PM", "comment": "Enrolled Employees/Enrolled Employees removed '52'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:08 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '90 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:08 PM", "comment": "Number of Eligible Employees changed from '68' to '-1'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:08 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:08 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:08 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:08 PM", "comment": "Policy/Group Number changed from '10812382' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:08 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 10:08:08 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/17/2025 10:42:44 PM", "comment": "Enrolled Employees/Enrolled Employees added '52'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "06/06/2025 08:57:22 AM", "comment": "Account Primary Sales Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/25/2024 01:34:06 PM", "comment": "Added HM - PPO Blue Sharing $2000 - Trucking renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/25/2024 01:34:06 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "07/01/2025 11:01:02 AM", "comment": "Edited Dental - Value N4"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:26:30 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:26:30 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:26:30 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:26:30 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:26:30 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:26:30 PM", "comment": "Policy/Group Number changed from '451893' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:26:30 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:26:30 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: ********' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "09/12/2024 12:11:08 PM", "comment": "Account Primary Service Lead automatically <NAME_EMAIL> to <EMAIL>"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/18/2024 12:07:22 PM", "comment": "Added Dental - Value N4 renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "07/18/2024 12:07:22 PM", "comment": "Created Plan by Copying a Current Plan."}]}, {"productID": ********, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:23:43 PM", "comment": "Enrolled Employees/Enrolled Employees removed '25'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:23:43 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:23:43 PM", "comment": "Number of Eligible Employees changed from '0' to '30'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:23:43 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:23:43 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:23:43 PM", "comment": "Origination Reason changed from 'Replacement' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:23:43 PM", "comment": "Policy/Group Number changed from 'CBWI' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:23:43 PM", "comment": "Commission Start Date changed from '10/01/2024' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:23:43 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24355839' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/17/2025 07:42:44 PM", "comment": "Enrolled Employees/Enrolled Employees added '25'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "09/19/2024 09:23:27 AM", "comment": "Added MERP replacement Plan"}]}, {"productID": 26352534, "logs": [{"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:23:14 PM", "comment": "Automatic Activity Log Creation for Renewal changed from '60 days' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:23:14 PM", "comment": "Premium Payment Frequency changed from 'per Month' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:23:14 PM", "comment": "Number of Eligible Employees changed from 'None Selected' to '0'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:23:14 PM", "comment": "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:23:14 PM", "comment": "Billing Type changed from 'Direct Bill' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:23:14 PM", "comment": "Origination Reason changed from 'Renewal' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:23:14 PM", "comment": "Policy/Group Number changed from '8367614' to ''"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:23:14 PM", "comment": "Commission Start Date changed from '01/01/2020' to 'None Selected'"}, {"userName": "<EMAIL>", "csrName": null, "date": "06/30/2025 02:23:14 PM", "comment": "Prior Plan changed from 'BP Internal Plan/Product ID: 24329582' to 'None Selected'"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "09/19/2024 09:15:52 AM", "comment": "Added Fashion Advantage Opt V renewal Plan"}, {"userName": "su<PERSON><PERSON>@benegroup.net", "csrName": null, "date": "09/19/2024 09:15:52 AM", "comment": "Created Plan by Copying a Current Plan."}]}]