import { Prisma } from '@prisma/client';
import { AccountIds, DEFAULT_FILTER } from 'common/constants';
import { StatementDataQueryDto } from 'common/dto/statement_data';
import Formatter from 'common/Formatter';
import { AgentCommissionsStatusesLabels } from 'common/globalTypes';
import { dateOrDefault, setNextDay, toArray } from 'common/helpers';
import { inject, injectable } from 'inversify';
import url from 'url';
import { customViewDefault } from 'common/constants/account_role_settings';
import { policyDataIfEmptyFields } from 'common/constants/report_data';

import { filterFieldOptions, loadFieldFilters } from '@/lib/helpers';
import { prismaClient } from '@/lib/prisma';
import {
  buildGlobalWhere,
  getSelectStatement,
  loadFilterOption,
  queryFieldValues,
} from '@/lib/statement';
import { AccountService } from '@/services/account';
import { ContactService } from '@/services/contact';
import {
  CommissionStatuses,
  ExtAccountInfo,
  ExtNextApiRequest,
  Roles,
} from '@/types';
import { ShareFilter } from '@/services/share/filter';

export type ApplyStatementFilterInput = {
  query: StatementDataQueryDto;
  where: any;
  account: ExtAccountInfo;
  queryParams: string[];
  userData: any;
  contactAndChildrenStrIds: string[];
};

@injectable()
export class StatementFilterService {
  @inject(AccountService)
  private accountService: AccountService;

  @inject(ShareFilter)
  private shareFilter: ShareFilter;

  getGlobalWhere(
    accountId,
    contactAndChildrenStrIds,
    contactStrId,
    q,
    recordId,
    includeDuplicates = false,
    includeLinked = false,
    currentRecord,
    tags,
    flags,
    showAllocatedCommissions = false
  ) {
    const globalWhere: { AND: any[] } = buildGlobalWhere(
      accountId,
      contactAndChildrenStrIds,
      contactStrId,
      q,
      recordId,
      includeDuplicates,
      includeLinked,
      currentRecord,
      showAllocatedCommissions
    );

    if (tags) {
      globalWhere.AND.push({ tags: { hasSome: tags } });
    }

    if (flags) {
      const flagsArray = Array.isArray(flags) ? flags : [flags];

      const flagsConditions = flagsArray.map((searchString) => {
        const [key, value] = searchString.split(':').map((str) => str.trim());

        if (!key) {
          throw new Error(
            `Invalid flag format: "${searchString}". Expected format "key: value".`
          );
        }

        if (key === DEFAULT_FILTER.BLANK_VALUE) {
          return { flags: { not: null } };
        }

        return {
          flags: {
            path: [key],
            equals: value,
          },
        };
      });

      globalWhere.AND.push({ OR: flagsConditions });
    }

    return globalWhere;
  }

  async getStatementFilters(
    account: ExtAccountInfo,
    req: ExtNextApiRequest,
    query: StatementDataQueryDto
  ) {
    const { producer_view, contacts } = query;
    const { account_id, role_id } = account;

    const groupNames = await this.shareFilter.getAllGroupNames({
      accountId: account_id,
      modelName: 'statement_data',
    });
    const transactionTypes = await this.shareFilter.getAllTransactionTypes({
      accountId: account_id,
      modelName: 'statement_data',
    });

    const userData = await this.getProducerUserData(
      account,
      producer_view,
      contacts
    );

    const contact_str_id = userData?.user_contact[0]?.str_id;
    const commissionsAccountSettings =
      await prismaClient.account_role_settings.findUnique({
        where: {
          account_id_role_id_custom_view_name: {
            account_id: String(account_id),
            role_id: parseInt(role_id),
            custom_view_name: customViewDefault,
          },
        },
        select: {
          pages_settings: true,
          agent_settings: true,
        },
      });

    const { agent_settings } = commissionsAccountSettings ?? {};

    const directDownlineDataAccess = (agent_settings as any)
      ?.directDownlineDataAccess?.commissionsConfig;
    const extendedDownlineDataAccess = (agent_settings as any)
      ?.extendedDownlineDataAccess?.commissionsConfig;
    let contactAndChildrenStrIds: string[] = [];
    if (contact_str_id) {
      contactAndChildrenStrIds = await this.getContactAndChildrenStrIds(
        extendedDownlineDataAccess,
        directDownlineDataAccess,
        contact_str_id
      );
    }
    const globalWhere = buildGlobalWhere(
      account.account_id,
      contactAndChildrenStrIds,
      contact_str_id,
      query.q,
      query.id,
      query.incl_dupes,
      query.incl_linked,
      null,
      query.show_allocated_commissions
    );

    const tags = await this.getStatementTags(globalWhere);

    const rawUrl = req.url;
    const parsedUrl = url.parse(rawUrl);
    const queryParamNames =
      parsedUrl.query?.split('&')?.map((param) => param.split('=')[0]) ?? [];
    const selectStatement = await getSelectStatement(
      commissionsAccountSettings
    );

    const { additionalFilterFields, filterList, where } =
      await this.applyStatementFilter({
        query,
        account,
        userData,
        contactAndChildrenStrIds,
        where: JSON.parse(JSON.stringify(globalWhere)),
        queryParams: queryParamNames,
      });
    const filters = await queryFieldValues(account_id, filterList, where);
    let _fieldOptions = filters;
    if (additionalFilterFields.length > 0) {
      const additionalFieldValues = await queryFieldValues(
        req.account_id,
        additionalFilterFields,
        globalWhere as Prisma.statement_dataWhereInput
      );
      _fieldOptions = { ..._fieldOptions, ...additionalFieldValues };
    }

    const start = await prismaClient.statement_data.findFirst({
      where: loadFilterOption(req, true),
      select: {
        payment_date: true,
      },
      orderBy: [{ ['payment_date']: 'asc' }],
    });
    const end = await prismaClient.statement_data.findFirst({
      where: loadFilterOption(req, true),
      select: {
        payment_date: true,
      },
      orderBy: [{ ['payment_date']: 'desc' }],
    });
    let fieldOptions = {
      contacts: undefined,
      tags: undefined,
      agent_commissions_status2: undefined,
      comp_calc_status: undefined,
      document_id: undefined,
      report_data_id: undefined,
      payment_date_start: start?.payment_date,
      payment_date_end: end?.payment_date,
      group_name: groupNames,
      transaction_type: transactionTypes,
      flags: [],
      ..._fieldOptions,
    };
    fieldOptions.contacts = Array.from(
      new Set(fieldOptions.contacts?.flat() ?? [])
    );
    fieldOptions.tags = tags;
    fieldOptions.agent_commissions_status2 = Object.values(
      AgentCommissionsStatusesLabels
    );
    fieldOptions.comp_calc_status = Object.values(
      AgentCommissionsStatusesLabels
    );
    // Add contact names to filters
    const contactIds = fieldOptions.contacts;
    const _contacts = await prismaClient.contacts.findMany({
      where: { str_id: { in: contactIds } },
      select: {
        str_id: true,
        first_name: true,
        last_name: true,
        email: true,
      },
    });

    const contactsMap = Object.fromEntries(
      _contacts.map((contact) => [
        contact.str_id,
        Formatter.contact(contact, { account_id: req.account_id }),
      ])
    );
    fieldOptions.contacts = [
      { id: '-1', name: DEFAULT_FILTER.BLANK_OPTION },
      ...contactIds.map((id) => ({
        id,
        name: contactsMap[id] || id,
      })),
    ];
    fieldOptions.contacts.sort((a, b) => a.name.localeCompare(b.name));

    const _flags = await prismaClient.statement_data.findMany({
      where: {
        flags: {
          not: null,
        },
      },
      select: {
        flags: true,
      },
    });

    const uniqueFlags = new Set(
      _flags.flatMap(({ flags }) =>
        Object.entries(flags).map(([key, value]) => `${key}: ${value}`)
      )
    );

    fieldOptions.flags = [
      { id: DEFAULT_FILTER.BLANK_VALUE, name: DEFAULT_FILTER.BLANK_OPTION },
      ...Array.from(uniqueFlags).map((flag) => {
        return { id: flag, name: flag };
      }),
    ];

    // Add document name to filters
    const documentIds = fieldOptions.document_id;
    const documents = await prismaClient.documents.findMany({
      where: { str_id: { in: documentIds } },
      select: {
        str_id: true,
        file_path: true,
        filename: true,
      },
    });
    const documentsMap = Object.fromEntries(
      documents.map((document) => [
        document.str_id,
        `${document?.file_path?.endsWith(document?.filename) ? document?.filename : `${document?.file_path?.split('/')?.pop()?.substring(22)}`}`,
      ])
    );
    fieldOptions.document_id =
      documentIds?.map((id) => ({
        id,
        name: documentsMap[id] || id,
      })) ?? [];
    fieldOptions.document_id.sort((a, b) => a.name.localeCompare(b.name));
    fieldOptions.report_data_id = ['Reconciled', 'Missing policy data'];
    const excludedKeys = ['payment_date_start', 'payment_date_end'];

    fieldOptions = filterFieldOptions(
      selectStatement,
      fieldOptions,
      excludedKeys
    ) as typeof fieldOptions;

    return fieldOptions;
  }

  async applyStatementFilter(params: ApplyStatementFilterInput) {
    const {
      query,
      where,
      account,
      queryParams,
      userData,
      contactAndChildrenStrIds,
    } = params;
    const {
      contacts,
      agent_commissions,
      report_data_id,
      hide_payout_calc_commissions,
      hide_no_payout_calc_commissions,
      is_commission_report,
      payment_date_start,
      payment_date_end,
      processing_date_start,
      processing_date_end,
      effective_date_start,
      effective_date_end,
      invoice_date_start,
      invoice_date_end,
      payment_date_empty,
      processing_date_empty,
      invoice_date_empty,
      effective_date_empty,
      statement_data_ids,
    } = query;
    const simpleFilterList = [
      'carrier_name',
      'status',
      'product_type',
      'product_name',
      'agent_name',
      'compensation_type',
      'payment_status',
      'writing_carrier_name',
      'document_id',
      'account_type',
      'transaction_type',
      'group_name',
    ];
    let filterList = [
      ...simpleFilterList,
      // TODO: Figure out why group by contacts not quite working as expected right now. Returning more than expected
      'contacts',
      'agent_commissions_status',
      'agent_commissions_status2',
      'reconciliation_status',
      'tags',
      'flags',
    ];
    const filteredQueryParamNames = queryParams.filter((param) =>
      filterList.includes(param)
    );
    const contact_str_id = userData?.user_contact[0]?.str_id;
    const additionalFilterFields = [];
    simpleFilterList.forEach((field) => {
      const value = query[field];
      if (!value) return;
      const filters = loadFieldFilters(value, field);

      if (filters.length > 0) {
        if (policyDataIfEmptyFields.includes(field)) {
          filters.push({
            report: {
              OR: structuredClone(filters),
            },
          });
        }

        where.AND.push({ OR: filters });
        if (filteredQueryParamNames.length > 0) {
          additionalFilterFields.push(field);
          filterList = filterList.filter((item) => item !== field);
        }
      }
    });

    if (query.comp_calc_status) {
      const compCalcStatusFilter: any[] = (
        Array.isArray(query.comp_calc_status)
          ? query.comp_calc_status
          : [query.comp_calc_status]
      ).map((status) => {
        // TODO: We need to refactor the filters to use enums for '(Blank)'
        if (status === DEFAULT_FILTER.BLANK_OPTION) {
          return {
            accounting_transaction_details: {
              none: {},
            },
          };
        } else {
          return {
            accounting_transaction_details: {
              some: {
                status: status,
              },
            },
          };
        }
      });
      where.AND.push({
        OR: compCalcStatusFilter,
      });
    }

    if (query.agent_commissions_status2) {
      if (is_commission_report && contact_str_id) {
        const agentPayoutFilter: any[] = toArray(
          query.agent_commissions_status2
        ).map((status) => ({
          agent_commissions_status2: {
            path: [contact_str_id],
            equals: status,
          },
        }));
        if (
          query.agent_commissions_status2.includes(DEFAULT_FILTER.BLANK_OPTION)
        ) {
          agentPayoutFilter.push({
            agent_commissions_status2: {
              path: [contact_str_id],
              equals: '',
            },
          });
          agentPayoutFilter.push({
            agent_commissions_status2: {
              path: [contact_str_id],
              equals: Prisma.DbNull,
            },
          });
          agentPayoutFilter.push({
            agent_commissions_status2: {
              path: [contact_str_id],
              equals: Prisma.JsonNull,
            },
          });
          agentPayoutFilter.push({
            agent_commissions_status2: { equals: Prisma.DbNull },
          });
        }

        where.AND.push({
          OR: agentPayoutFilter,
        });
      } else {
        const contactValues: any = await queryFieldValues(
          account.account_id,
          ['contacts'],
          {
            account_id: account.account_id,
            state: 'active',
          }
        );
        const contactStrIds = contactValues.contacts.flat();

        where.AND.push({
          OR: toArray(query.agent_commissions_status2).flatMap((status) =>
            contactStrIds.map((contactId) => ({
              agent_commissions_status2: {
                path: [contactId],
                equals: status,
              },
            }))
          ),
        });
      }
    }

    if (query.agent_commissions_status) {
      const conditions = Array.isArray(query.agent_commissions_status)
        ? query.agent_commissions_status.map((status) => ({
            agent_commissions_status: {
              equals:
                status === DEFAULT_FILTER.BLANK_OPTION ? Prisma.DbNull : status,
            },
          }))
        : [
            {
              agent_commissions_status: {
                equals:
                  query.agent_commissions_status === DEFAULT_FILTER.BLANK_OPTION
                    ? Prisma.DbNull
                    : query.agent_commissions_status,
              },
            },
          ];

      if (
        query.agent_commissions_status.includes(DEFAULT_FILTER.BLANK_OPTION)
      ) {
        conditions.push({
          agent_commissions_status: {
            equals: '',
          },
        });
      }

      if (conditions.length) {
        additionalFilterFields.push('agent_commissions_status');
        filterList = filterList.filter(
          (item) => item !== 'agent_commissions_status'
        );
      }
      where.AND.push({ OR: conditions });
    }

    // Exclude zero commissions from the report, unless incl_linked is true.
    // Linked reports could have zero commissions.
    if (query.is_commission_report && !query.incl_linked) {
      where.AND.push({
        OR: [
          {
            agent_commissions: {
              path: [contact_str_id],
              gte: 0.001,
            },
          },
          {
            agent_commissions: {
              path: [contact_str_id],
              lte: -0.001,
            },
          },
        ],
      });
    }

    if (contacts?.length > 0 || agent_commissions?.length > 0) {
      where.AND.push({
        OR: [
          contacts
            ? {
                contacts: {
                  hasSome: Array.isArray(contacts) ? contacts : [contacts],
                },
              }
            : {},
          Array.isArray(contacts) && contacts.includes('-1')
            ? { contacts: { isEmpty: true } }
            : {},
          agent_commissions
            ? {
                agent_commissions: {
                  path: Array.isArray(agent_commissions)
                    ? agent_commissions
                    : [agent_commissions],
                  not: null,
                },
              }
            : {},
        ],
      });
      if (filteredQueryParamNames.length > 0) {
        additionalFilterFields.push(
          filteredQueryParamNames[0] === 'contacts'
            ? 'contacts'
            : 'agent_commissions'
        );
        filterList = filterList.filter(
          (item) =>
            item !==
            (filteredQueryParamNames[0] === 'contacts'
              ? 'contacts'
              : 'agent_commissions')
        );
      }
    }
    // Not currently supporting filtering individual report_data_ids
    // Using as a proxy for matched (reconciled) or not
    if (Array.isArray(report_data_id)) {
      const filters = [];
      report_data_id.forEach((id) => {
        if (id.toLowerCase() === 'reconciled') {
          filters.push({
            report_data_id: { not: null },
          });
        }
        if (id.toLowerCase() === 'missing policy data') {
          filters.push({
            report_data_id: { equals: null },
          });
        }
      });
      if (filters.length > 0) {
        where.AND.push({ OR: filters });
        if (filteredQueryParamNames.length > 0) {
          additionalFilterFields.push('report_data_id');
          filterList = filterList.filter((item) => item !== 'report_data_id');
        }
      }
    }

    // Filter used to filter by ids comming from comp report
    if (Array.isArray(statement_data_ids)) {
      where.AND.push({
        id: { in: statement_data_ids },
      });
    }

    if (hide_payout_calc_commissions) {
      where.AND.push({
        OR: [
          {
            agent_commissions: {
              equals: Prisma.AnyNull,
            },
          },
        ],
      });
    }

    if (hide_no_payout_calc_commissions) {
      where.AND.push({
        OR: [
          {
            agent_commissions: {
              not: Prisma.AnyNull,
            },
          },
        ],
      });
    }

    if (
      !is_commission_report &&
      +account.role_id === Roles.PRODUCER &&
      contact_str_id
    ) {
      const path =
        contactAndChildrenStrIds.length > 0
          ? contactAndChildrenStrIds
          : [contact_str_id];
      where.AND.push(StatementFilterService.agentCommissionWhereClause(path));

      const isShowOnlyPaidCommissionsProducers =
        await this.accountService.isShowOnlyPaidCommissionsProducers(
          account.account_id
        );
      if (isShowOnlyPaidCommissionsProducers)
        if (account.account_id === AccountIds.TRAILSTONE) {
          // TODO: Make this configurable from account settings
          where.AND.push({
            OR: [
              {
                agent_commissions_status: {
                  equals: CommissionStatuses.PAID,
                },
              },
            ],
          });
        } else {
          where.AND.push({
            OR: [
              {
                agent_commissions_status: {
                  equals: CommissionStatuses.APPROVED,
                },
              },
              { agent_commissions_status: { equals: CommissionStatuses.PAID } },
            ],
          });
        }
    }
    const effectiveDateEmpty = effective_date_empty;
    const effectiveDateEnd = setNextDay(
      dateOrDefault(effective_date_end, undefined)
    );
    const effectiveDateStart = dateOrDefault(effective_date_start, undefined);
    const paymentDateEmpty = payment_date_empty;
    const paymentDateEnd = setNextDay(
      dateOrDefault(payment_date_end, undefined)
    );
    const paymentDateStart = dateOrDefault(payment_date_start, undefined);
    const processingDateEmpty = processing_date_empty;
    const processingDateEnd = setNextDay(
      dateOrDefault(processing_date_end, undefined)
    );
    const processingDateStart = dateOrDefault(processing_date_start, undefined);
    const statementDateEmpty = invoice_date_empty;
    const statementDateEnd = setNextDay(
      dateOrDefault(invoice_date_end, undefined)
    );
    const statementDateStart = dateOrDefault(invoice_date_start, undefined);

    where.AND = [
      ...where.AND,
      {
        OR: [
          {
            AND: [
              {
                payment_date: paymentDateStart
                  ? { gte: paymentDateStart }
                  : undefined,
              },
              {
                payment_date: paymentDateEnd
                  ? { lt: paymentDateEnd }
                  : undefined,
              },
              {
                processing_date: processingDateStart
                  ? { gte: processingDateStart }
                  : undefined,
              },
              {
                processing_date: processingDateEnd
                  ? { lt: processingDateEnd }
                  : undefined,
              },
              {
                invoice_date: statementDateStart
                  ? { gte: statementDateStart }
                  : undefined,
              },
              {
                invoice_date: statementDateEnd
                  ? { lt: statementDateEnd }
                  : undefined,
              },
              {
                effective_date: effectiveDateStart
                  ? { gte: effectiveDateStart }
                  : undefined,
              },
              {
                effective_date: effectiveDateEnd
                  ? { lt: effectiveDateEnd }
                  : undefined,
              },
            ],
          },
          {
            OR: [
              { payment_date: paymentDateEmpty ? { equals: null } : undefined },
              {
                processing_date: processingDateEmpty
                  ? { equals: null }
                  : undefined,
              },
              {
                invoice_date: statementDateEmpty ? { equals: null } : undefined,
              },
              {
                effective_date: effectiveDateEmpty
                  ? { equals: null }
                  : undefined,
              },
            ],
          },
        ],
      },
    ];

    return { where, additionalFilterFields, filterList };
  }

  async getProducerUserData(
    account: ExtAccountInfo,
    producer_view: boolean,
    contacts: string[] | string
  ) {
    if (producer_view) {
      account.role_id = Roles.PRODUCER.toString();
    }
    if (+account.role_id !== Roles.PRODUCER) return null;
    let userData;
    userData = await prismaClient.users.findUnique({
      where: { uid: String(account.uid), state: 'active' },
      select: {
        user_contact: {
          where: { state: 'active' },
        },
      },
    });
    if (Array.isArray(contacts)) {
      userData = { user_contact: [{ str_id: contacts[0] }] };
    } else if (contacts) {
      userData = { user_contact: [{ str_id: contacts }] };
    } else {
      console.warn('No contact id found for producer');
    }
    if (!userData?.user_contact[0]?.str_id) {
      throw new Error(`No contact id found for producer ${account.uid}`);
    }
    return userData;
  }

  async getContactAndChildrenStrIds(
    extendedDownlineDataAccess: string,
    directDownlineDataAccess: string,
    contact_str_id: string
  ) {
    const contactService = new ContactService();
    if (extendedDownlineDataAccess === 'Yes') {
      return await contactService.getChildrenContactStrIdListByDepth(
        contact_str_id,
        10
      );
    } else if (directDownlineDataAccess === 'Yes') {
      return await contactService.getChildrenContactStrIdListByDepth(
        contact_str_id,
        1
      );
    }
    return [];
  }

  static agentCommissionWhereClause(paths: string[]) {
    return {
      OR: paths.map((path) => ({
        OR: [
          { agent_commissions: { path: [path], not: Prisma.DbNull } },
          { agent_commissions: { path: [path], not: 0 } },
        ],
      })),
    };
  }

  async getStatementTags(condition) {
    const statementData = await prismaClient.statement_data.findMany({
      where: condition,
      select: {
        tags: true,
      },
    });
    const statementTags = new Set();
    statementData.forEach((row) => {
      if (row.tags) {
        row.tags.forEach((tag) => {
          statementTags.add(tag);
        });
      }
    });
    const result: any = [DEFAULT_FILTER.BLANK_OPTION];
    Array.from(statementTags).forEach((tag) => {
      result.push(tag);
    });
    return result;
  }
}
