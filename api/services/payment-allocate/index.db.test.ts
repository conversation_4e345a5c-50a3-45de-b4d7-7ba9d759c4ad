import { describe, expect, it, vi, beforeEach } from 'vitest';
import { Prisma, statement_data, virtual_type } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';

import { PaymentAllocateService } from './index';
import { testGuard } from '@/lib/testGuard';
import { prismaClient } from '@/lib/prisma';
import { DataStates } from '@/types';

type StatementWithDetails = Prisma.statement_dataGetPayload<{
  include: {
    details: true;
  };
}>;

const createStatement = (
  id: number,
  commission: number,
  remain: number | null = null,
  other: Partial<statement_data> = {}
): StatementWithDetails =>
  ({
    id,
    commission_amount: new Decimal(commission),
    remain_amount:
      remain !== null ? new Decimal(remain) : new Decimal(commission),
    details: [],
    ...other,
  }) as StatementWithDetails;

describe('PaymentAllocateService', () => {
  let service: PaymentAllocateService;

  beforeEach(() => {
    service = new PaymentAllocateService();
    vi.clearAllMocks();
  });
  testGuard(() => {
    describe('getPolicies', () => {
      const accountId = 'test-get-policies-account-id';

      const createReportData = async (
        data: Partial<Prisma.report_dataCreateInput>
      ) => {
        return prismaClient.report_data.create({
          data: {
            account_id: accountId,
            policy_id: `policy-${Date.now()}-${Math.random()}`,
            state: DataStates.ACTIVE,
            premium_amount: new Decimal(1000),
            ...data,
          },
        });
      };

      const createStatementData = async (
        data: Partial<Prisma.statement_dataCreateInput>
      ) => {
        return prismaClient.statement_data.create({
          data: {
            account_id: accountId,
            state: DataStates.ACTIVE,
            remain_amount: new Decimal(100),
            ...data,
          },
        });
      };

      beforeEach(async () => {
        await prismaClient.statement_data.deleteMany({
          where: { account_id: accountId },
        });
        await prismaClient.report_data.deleteMany({
          where: { account_id: accountId },
        });
      });

      it('should return empty array when no policies exist', async () => {
        const policies = await service.getPolicies(accountId);
        expect(policies).toEqual([]);
      });

      it('should return policies for given account_id', async () => {
        await createReportData({
          policy_id: 'policy-1',
          premium_amount: new Decimal(1000),
        });
        await createReportData({
          policy_id: 'policy-2',
          premium_amount: new Decimal(2000),
        });

        await createStatementData({
          policy_id: 'policy-1',
        });
        await createStatementData({
          policy_id: 'policy-2',
        });

        const policies = await service.getPolicies(accountId);
        expect(policies).toHaveLength(2);
        expect(policies.map((p) => p.policy_id)).toEqual(
          expect.arrayContaining(['policy-1', 'policy-2'])
        );
      });

      it('should order policies by policy_id and effective_date', async () => {
        await createReportData({
          policy_id: 'policy-older',
        });

        await createReportData({
          policy_id: 'policy-newer',
        });

        await createStatementData({
          policy_id: 'policy-older',
        });
        await createStatementData({
          policy_id: 'policy-newer',
        });

        const policies = await service.getPolicies(accountId);
        expect(policies).toHaveLength(2);
        expect(policies[0].policy_id).toBe('policy-newer');
        expect(policies[1].policy_id).toBe('policy-older');
      });

      it('should only return policies for specified account_id', async () => {
        const otherAccountId = 'other-account-id';

        await createReportData({
          policy_id: 'policy-for-test-account',
        });

        await prismaClient.report_data.create({
          data: {
            account_id: otherAccountId,
            policy_id: 'policy-for-other-account',
            state: DataStates.ACTIVE,
            premium_amount: new Decimal(1000),
          },
        });

        await createStatementData({
          policy_id: 'policy-for-test-account',
        });

        await prismaClient.statement_data.create({
          data: {
            account_id: otherAccountId,
            policy_id: 'policy-for-other-account',
            state: DataStates.ACTIVE,
            remain_amount: new Decimal(100),
          },
        });

        const policies = await service.getPolicies(accountId);
        expect(policies).toHaveLength(1);
        expect(policies[0].policy_id).toBe('policy-for-test-account');
        expect(policies[0].account_id).toBe(accountId);

        await prismaClient.statement_data.deleteMany({
          where: { account_id: otherAccountId },
        });
        await prismaClient.report_data.deleteMany({
          where: { account_id: otherAccountId },
        });
      });

      it('should return empty array when policies exist but no statements exist', async () => {
        await createReportData({
          policy_id: 'policy-without-statements',
        });

        const policies = await service.getPolicies(accountId);
        expect(policies).toEqual([]);
      });

      it('should only return policies that have corresponding statements', async () => {
        await createReportData({
          policy_id: 'policy-with-statements',
        });
        await createReportData({
          policy_id: 'policy-without-statements',
        });

        await createStatementData({
          policy_id: 'policy-with-statements',
        });

        const policies = await service.getPolicies(accountId);
        expect(policies).toHaveLength(1);
        expect(policies[0].policy_id).toBe('policy-with-statements');
      });
    });
  });
  describe('calculateAllocations', () => {
    it('should return isFulfilled true when targetAmount is 0', () => {
      const statements = [createStatement(1, 100)];
      const targetAmount = new Decimal(0);
      const { allocations, isFulfilled } = service.calculateAllocations(
        statements,
        targetAmount
      );
      expect(allocations).toHaveLength(0);
      expect(isFulfilled).toBe(true);
    });

    it('should allocate nothing if statements are empty', () => {
      const statements: StatementWithDetails[] = [];
      const targetAmount = new Decimal(100);
      const { allocations, isFulfilled } = service.calculateAllocations(
        statements,
        targetAmount
      );
      expect(allocations).toHaveLength(0);
      expect(isFulfilled).toBe(false);
    });

    it('should allocate partially when one statement is enough', () => {
      const statements = [createStatement(1, 100)];
      const targetAmount = new Decimal(50);
      const { allocations, isFulfilled } = service.calculateAllocations(
        statements,
        targetAmount
      );
      expect(allocations).toHaveLength(1);
      expect(allocations[0].statement.id).toBe(1);
      expect(allocations[0].amount.equals(new Decimal(50))).toBe(true);
      expect(isFulfilled).toBe(true);
    });

    it('should allocate fully one statement', () => {
      const statements = [createStatement(1, 100)];
      const targetAmount = new Decimal(100);
      const { allocations, isFulfilled } = service.calculateAllocations(
        statements,
        targetAmount
      );
      expect(allocations).toHaveLength(1);
      expect(allocations[0].statement.id).toBe(1);
      expect(allocations[0].amount.equals(new Decimal(100))).toBe(true);
      expect(isFulfilled).toBe(true);
    });

    it('should allocate multiple statements to meet target', () => {
      const statements = [createStatement(1, 50), createStatement(2, 50)];
      const targetAmount = new Decimal(100);
      const { allocations, isFulfilled } = service.calculateAllocations(
        statements,
        targetAmount
      );
      expect(allocations).toHaveLength(2);
      expect(allocations[0].amount.equals(new Decimal(50))).toBe(true);
      expect(allocations[1].amount.equals(new Decimal(50))).toBe(true);
      expect(isFulfilled).toBe(true);
    });

    it('should allocate partially from the last statement to meet target', () => {
      const statements = [createStatement(1, 50), createStatement(2, 100)];
      const targetAmount = new Decimal(100);
      const { allocations, isFulfilled } = service.calculateAllocations(
        statements,
        targetAmount
      );
      expect(allocations).toHaveLength(2);
      expect(allocations[0].amount.equals(new Decimal(50))).toBe(true);
      expect(allocations[1].amount.equals(new Decimal(50))).toBe(true);
      expect(isFulfilled).toBe(true);
    });

    it('should not be fulfilled if total is less than target', () => {
      const statements = [createStatement(1, 50)];
      const targetAmount = new Decimal(100);
      const { allocations, isFulfilled } = service.calculateAllocations(
        statements,
        targetAmount
      );
      expect(allocations).toHaveLength(1);
      expect(allocations[0].amount.equals(new Decimal(50))).toBe(true);
      expect(isFulfilled).toBe(false);
    });

    it('should use remain_amount when available', () => {
      const statements = [createStatement(1, 100, 30)];
      const targetAmount = new Decimal(50);
      const { allocations, isFulfilled } = service.calculateAllocations(
        statements,
        targetAmount
      );
      expect(allocations).toHaveLength(1);
      expect(allocations[0].amount.equals(new Decimal(30))).toBe(true);
      expect(isFulfilled).toBe(false);
    });

    it('should ignore statements with zero or negative remain_amount', () => {
      const statements = [
        createStatement(1, 100, 0),
        createStatement(2, 100, -10),
        createStatement(3, 50),
      ];
      const targetAmount = new Decimal(100);
      const { allocations, isFulfilled } = service.calculateAllocations(
        statements,
        targetAmount
      );
      expect(allocations).toHaveLength(1);
      expect(allocations[0].statement.id).toBe(3);
      expect(allocations[0].amount.equals(new Decimal(50))).toBe(true);
      expect(isFulfilled).toBe(false);
    });
  });

  describe('groupedTotalAmount', () => {
    it('should sum commission_amount from children_data', () => {
      const statement = {
        children_data: [
          { commission_amount: new Decimal(25) },
          { commission_amount: new Decimal(75) },
          { commission_amount: null },
          { commission_amount: new Decimal(10) },
        ],
      };
      const total = service.groupedTotalAmount(statement as any);
      expect(total.equals(new Decimal(110))).toBe(true);
    });

    it('should return 0 if no children_data', () => {
      const statement = {
        children_data: [],
      };
      const total = service.groupedTotalAmount(statement as any);
      expect(total.equals(new Decimal(0))).toBe(true);
    });
  });

  describe('cloneStatement', () => {
    it('should clone a statement and remove specific fields', () => {
      const originalStatement: statement_data = {
        id: 1,
        account_id: 'acc_123',
        commission_amount: new Decimal('100.50'),
        state: 'ACTIVE',
        agent_commissions_status: 'pending',
        created_at: new Date(),
        updated_at: new Date(),
        details: [],
        str_id: 'str_123',
        is_virtual: false,
        virtual_type: null,
      } as any;

      const cloned = service.cloneStatement(originalStatement);

      expect(cloned).not.toHaveProperty('id');
      expect(cloned).not.toHaveProperty('created_at');
      expect(cloned).not.toHaveProperty('updated_at');
      expect(cloned).not.toHaveProperty('agent_commissions_status');
      expect(cloned).not.toHaveProperty('details');
      expect(cloned).not.toHaveProperty('str_id');
      expect(cloned).not.toHaveProperty('is_virtual');
      expect(cloned).not.toHaveProperty('virtual_type');
      expect(cloned).toHaveProperty('account_id', 'acc_123');
      expect(cloned.commission_amount.equals(new Decimal('100.50'))).toBe(true);
    });
  });

  describe('getStatementRemainingAmount', () => {
    it('should return remain_amount if it is set', async () => {
      const statement = {
        id: 1,
        commission_amount: new Decimal(100),
        remain_amount: new Decimal(25),
        details: [
          {
            virtual_type: virtual_type.partial_payment,
            commission_amount: new Decimal(20),
          },
        ],
      };

      const mockPrisma = {
        statement_data: {
          findFirst: vi.fn().mockResolvedValue(statement),
        },
      };

      const remaining = await service.getStatementRemainingAmount(
        statement as any,
        mockPrisma as any
      );
      expect(remaining.equals(new Decimal(25))).toBe(true);
    });

    it('should calculate remaining amount from partial payment details when remain_amount is null', async () => {
      const statement = {
        id: 1,
        commission_amount: new Decimal(100),
        remain_amount: null,
        details: [
          {
            virtual_type: virtual_type.partial_payment,
            commission_amount: new Decimal(20),
          },
          {
            virtual_type: virtual_type.partial_payment,
            commission_amount: new Decimal(30),
          },
          {
            virtual_type: virtual_type.grouped,
            commission_amount: new Decimal(10),
          },
        ],
      };

      const mockPrisma = {
        statement_data: {
          findFirst: vi.fn().mockResolvedValue(statement),
        },
      };

      const remaining = await service.getStatementRemainingAmount(
        statement as any,
        mockPrisma as any
      );
      expect(remaining.equals(new Decimal(50))).toBe(true);
    });

    it('should return full amount if no partial payment details and remain_amount is null', async () => {
      const statement = {
        id: 1,
        commission_amount: new Decimal(100),
        remain_amount: null,
        details: [
          {
            virtual_type: virtual_type.grouped,
            commission_amount: new Decimal(10),
          },
        ],
      };

      const mockPrisma = {
        statement_data: {
          findFirst: vi.fn().mockResolvedValue(statement),
        },
      };

      const remaining = await service.getStatementRemainingAmount(
        statement as any,
        mockPrisma as any
      );
      expect(remaining.equals(new Decimal(100))).toBe(true);
    });

    it('should return full amount if no details array and remain_amount is null', async () => {
      const statement = {
        id: 1,
        commission_amount: new Decimal(100),
        remain_amount: null,
        details: [],
      };

      const mockPrisma = {
        statement_data: {
          findFirst: vi.fn().mockResolvedValue(statement),
        },
      };

      const remaining = await service.getStatementRemainingAmount(
        statement as any,
        mockPrisma as any
      );
      expect(remaining.equals(new Decimal(100))).toBe(true);
    });

    it('should return 0 if statement is not found', async () => {
      const statement = {
        id: 1,
        commission_amount: new Decimal(100),
      };

      const mockPrisma = {
        statement_data: {
          findFirst: vi.fn().mockResolvedValue(null),
        },
      };

      const remaining = await service.getStatementRemainingAmount(
        statement as any,
        mockPrisma as any
      );
      expect(remaining.equals(new Decimal(0))).toBe(true);
    });
  });

  testGuard(() => {
    describe('getAllocatableStatements', () => {
      const accountId = 'test-account-id';
      const policyId = 'test-policy-id';

      const createReportData = async (config = {}) => {
        return prismaClient.report_data.create({
          data: {
            account_id: accountId,
            policy_id: policyId,
            state: DataStates.ACTIVE,
            premium_amount: new Decimal(1200),
            config,
          },
        });
      };

      const createStatementData = async (
        data: Partial<Prisma.statement_dataCreateInput>
      ) => {
        return prismaClient.statement_data.create({
          data: {
            account_id: accountId,
            policy_id: policyId,
            state: DataStates.ACTIVE,
            remain_amount: new Decimal(100),
            ...data,
          },
        });
      };

      beforeEach(async () => {
        await prismaClient.statement_data.deleteMany({
          where: { account_id: accountId },
        });
        await prismaClient.report_data.deleteMany({
          where: { account_id: accountId },
        });
      });

      it('should return all allocatable active statements', async () => {
        const policy = await createReportData();
        await createStatementData({
          product_type: 'Dental',
          remain_amount: new Decimal(100),
        });
        await createStatementData({
          product_type: 'Vision',
          state: DataStates.ALLOCATED,
          remain_amount: new Decimal(50),
        });
        // Should be ignored
        await createStatementData({
          product_type: 'Medical',
          remain_amount: new Decimal(0),
        });
        await createStatementData({
          product_type: 'Other',
          state: DataStates.GROUPED,
        });

        const baseStatement = { account_id: accountId, policy_id: policyId };
        const statements = await service.getAllocatableStatements(
          baseStatement as any,
          policy
        );

        expect(statements).toHaveLength(3);
        expect(statements.map((s) => s.product_type)).toEqual(
          expect.arrayContaining(['Dental', 'Vision', 'Other'])
        );
      });

      it('should only return dental products if rule is dental_only', async () => {
        const policy = await createReportData({
          allied_payment_rule: { mode: 'dental_only' },
        });
        await createStatementData({ product_type: 'Dental' });
        await createStatementData({ product_type: 'Vision' });

        const baseStatement = { account_id: accountId, policy_id: policyId };
        const statements = await service.getAllocatableStatements(
          baseStatement as any,
          policy
        );

        expect(statements).toHaveLength(1);
        expect(statements[0].product_type).toBe('Dental');
      });

      it('should return dental and vision products if rule is dental_vision', async () => {
        const policy = await createReportData({
          allied_payment_rule: { mode: 'dental_vision' },
        });
        await createStatementData({ product_type: 'Dental' });
        await createStatementData({ product_type: 'Vision' });
        await createStatementData({ product_type: 'Medical' });

        const baseStatement = { account_id: accountId, policy_id: policyId };
        const statements = await service.getAllocatableStatements(
          baseStatement as any,
          policy
        );

        expect(statements).toHaveLength(2);
        expect(statements.map((s) => s.product_type)).toEqual(
          expect.arrayContaining(['Dental', 'Vision'])
        );
      });

      it('should sort by priority when rule has priority', async () => {
        const policy = await createReportData({
          allied_payment_rule: {
            mode: 'dental_vision',
            priority: ['Vision', 'Dental'],
          },
        });
        await createStatementData({
          product_type: 'Dental',
          remain_amount: new Decimal(100),
        });
        await createStatementData({
          product_type: 'Vision',
          remain_amount: new Decimal(50),
        });

        const baseStatement = { account_id: accountId, policy_id: policyId };
        const statements = await service.getAllocatableStatements(
          baseStatement as any,
          policy
        );

        expect(statements).toHaveLength(2);
        expect(statements.map((s) => s.product_type)).toEqual([
          'Vision',
          'Dental',
        ]);
      });

      it('should sort by period_date by default if no priority is given', async () => {
        const policy = await createReportData({
          allied_payment_rule: { mode: 'dental_vision' },
        });
        await createStatementData({
          product_type: 'Dental',
          remain_amount: new Decimal(100),
          period_date: new Date('2023-01-02'),
        });
        await createStatementData({
          product_type: 'Vision',
          remain_amount: new Decimal(50),
          period_date: new Date('2023-01-01'),
        });

        const baseStatement = { account_id: accountId, policy_id: policyId };
        const statements = await service.getAllocatableStatements(
          baseStatement as any,
          policy
        );
        expect(statements.map((s) => s.product_type)).toEqual([
          'Vision',
          'Dental',
        ]);
      });
    });
  });
});
