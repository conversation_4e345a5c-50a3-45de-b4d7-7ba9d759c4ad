###
# @name Fetch processors where type=policies-report,commissions-report
# @import ./auth.http
# @ref auth
GET {{baseUrl}}/api/processors?type=policies-report,commissions-report&status=approved
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{fintaryAdminAccountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 

> {% 
  client.test("Given processors, when fetching by type, should return at least one policies-report and one commissions-report", function() {
    client.assert(response.status === 200, "Response status is not 200");
    const policies = response.body.filter(p => p.type === "policies-report");
    const commissions = response.body.filter(p => p.type === "commissions-report");
    client.assert(policies.length > 0, `No policies-report processor found, ${JSON.stringify(response.body)}`);
    client.assert(commissions.length > 0, "No commissions-report processor found");
    client.global.set("policyProcessorId", policies[0].str_id);
    client.global.set("commissionsProcessorId", commissions[0].str_id);
  });
%}

###
# @name Fetch commissions report (json only)
# @import ./auth.http
# @ref auth 
GET {{baseUrl}}/api/export
?orderBy=created_at&sort=desc
&endpoint=statement_data
&json_only=true
&limit=50
&page=0
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{fintaryAdminAccountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 

> {% 
  client.test("Given commissions report endpoint, when fetching statement_data, then should return 2xx", function() {
    client.assert(response.status >= 200 && response.status < 300, "Response status is not 2xx");
    // client.assert("Agent comp calc" in response.body[0], "Response body does not contain 'Agent comp calc'");
  });
%}

###
# @name Fetch commissions report (with report processor)
# @import ./auth.http
# @ref auth
GET {{baseUrl}}/api/export
?orderBy=created_at&sort=desc
&endpoint=statement_data
&report_processor={{policyProcessorId}}
&limit=50
&page=0
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{fintaryAdminAccountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 

> {% 
  client.test("Given commissions report endpoint with policyProcessorId, when fetching statement_data, then should return 2xx", function() {
    client.assert(response.status >= 200 && response.status < 300, "Response status is not 2xx");
  });
%}

###
# @name Fetch policies report (json only)
# @import ./auth.http
# @ref auth
GET {{baseUrl}}/api/export
?orderBy=created_at&sort=desc
&endpoint=report_data
&json_only=true
&limit=50
&page=0
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{fintaryAdminAccountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 

> {% 
  client.test("Given policies report endpoint, when fetching report_data, then should return 2xx", function() {
    client.assert(response.status >= 200 && response.status < 300, "Response status is not 2xx");
    // client.assert("Policy date" in response.body[0], "Response body does not contain 'Policy date'");
  });
%}

###
# @name Fetch policies report (with report processor)
# @import ./auth.http
# @ref auth
GET {{baseUrl}}/api/export
?orderBy=created_at&sort=desc
&endpoint=report_data
&report_processor={{policyProcessorId}}
&limit=50
&page=0
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{fintaryAdminAccountId}} 
authentication: Bearer {{fintaryAdminIdToken}}

> {% 
  client.test("Given policies report endpoint with policyProcessorId, when fetching report_data, then should return 2xx", function() {
    client.assert(response.status >= 200 && response.status < 300, "Response status is not 2xx");
  });
%}
