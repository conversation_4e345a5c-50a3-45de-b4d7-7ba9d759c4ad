# Introduction

As part of our efforts to document, catalog, and effectively test our APIs,
we're introducing the
[IntelliJ .http standard](https://www.jetbrains.com/help/idea/exploring-http-syntax.html) +
[httpyac CLI+IDE tooling](https://httpyac.github.io) for running `.http`
collections. This is a light weight alternative to Postman that is human
readable, easily executable via CLI, and which has rich IDE support in VS Code.

# First time setup

As of now, the `http` suite is only intended for use by `Docker.integrations`. In the future, it will be integrated into our `local-db:seed`  script. But for now, please compose and run all tests via Docker.

# Running tests

You can use docker to run your tests.

`docker-compose -f docker-compose.integration.yaml down -v && docker-compose -f docker-compose.integration.yaml up --build --force-recreate --abort-on-container-exit --exit-code-from integration`

# Adding new tests

## Prep your seed data

Integration tests require specific seed data to be available in the database. Our integration seed data is managed through separate files in the `prisma/` directory:

### Seed Data Structure

- **Main seed file**: `prisma/seed.integration.ts` - orchestrates all seed data creation
- **Individual seed modules**: `*SeedData.integration.ts` files for each data type

### Adding New Seed Data

1. **Create a new seed data file**: `prisma/yourDataSeedData.integration.ts`
2. **Export an async function** that uses `prisma.upsert()` to create/update records:
   ```typescript
   import { prisma } from './seed.integration';
   
   export async function yourDataSeedData() {
     await prisma.yourTable.upsert({
       where: { str_id: 'unique-test-id' },
       update: {},
       create: {
         // your test data here
       },
     });
   }
   ```
3. **Import and call it** in `seed.integration.ts`:
   ```typescript
   import { yourDataSeedData } from './yourDataSeedData.integration';
   
   async function main() {
     // ... existing seed calls
     await yourDataSeedData();
   }
   ```

## Write a `.http` file

### File Structure & Naming

- Name all integration test files using the `{{domain/object}}.http` format (e.g., `export_report.http`, `companies.http`)
- If you need auth, use the `@import` and `@ref` decorators. See [caveats](#caveats) for more info.

### Using Seed Data in Tests

Reference the seeded data in your `.http` files using the pre-defined IDs from the integration seed data:

```http
###
# @name Test API endpoint with seeded data
# @import ./auth.http
# @ref fintaryAdmin
GET {{baseUrl}}/api/some-endpoint
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: 2gRRbMuTSXxTTX8d4WFwO
authentication: Bearer {{fintaryAdminIdToken}}
```

**Available seeded account IDs:**
- `2gRRbMuTSXxTTX8d4WFwO` - "Integration Test Fintary Admin" account
- `HabSqiMWvzKVnK17nEZQY` - "Integration Customer Account"

**Important**: Always use `upsert()` instead of `create()` to ensure tests can run multiple times without conflicts.

### Example Test Structure

Each test must be separated by a ### or they might be skipped.

```http
###
# @name Fetch processors by type
# @import ./auth.http
# @ref fintaryAdmin 
GET {{baseUrl}}/api/processors?type=policies-report,commissions-report&status=approved
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{fintaryAdminAccountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 

> {% 
  client.test("Should return processors", function() {
    client.assert(response.status === 200, "Response status is not 200");
    client.assert(response.body.length > 0, "No processors found");
  });
%}
```

### Caveats

- Wrap string variables in quotation marks in your body or else you will receive
  a JSON parsing error. E.g.

```json
{
  "company_name": "Tests Inc.",
  "email": "<EMAIL>",
  "website": "https://test.com",
  "phone": "**********",
  "address": "123 Rainey Street",
  "notes": "Just testing the create company function.",
  "type": [
    "Customer"
  ],
  "uid": "{{uid}}",
  "account_id": "{{accountId}}"
}
```

- If you need auth in a file, include and reference `auth.http` in your file on
  the first request. E.g.

```http
### 
# @name GET Session Check - Invalid Token
# @import ./auth.http
# @ref auth
GET {{baseUrl}}/api/session/check
```

- When sending JSON, ensure that you add the `Content-Type` header to your
  request or else you will send a string and not a JSON object. E.g.