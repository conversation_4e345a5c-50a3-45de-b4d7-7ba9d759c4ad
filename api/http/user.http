###
# @name Login (new user)
# @import ./auth.http
# @ref newUser 
POST {{baseUrl}}/api/login
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{accountId}} 
authentication: Bearer {{newUserIdToken}} 
userrole: 1

{ "email": "{{newUserEmail}}"}

> {% 
  client.global.set("newAccountStrId", response.body.userAccounts[0].account.str_id)  
  client.test("Given valid firebase user, when creating fintary user, should return 200 ok", function() {
    client.assert(response.status === 200, "Response status is not 200");
    client.assert("userEmail" in response.body, "Response body does not contain userEmail key");
  })
%}

###
# @name Get user
GET {{baseUrl}}/api/users
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{newAccountStrId}} 
authentication: Bearer {{newUserIdToken}} 
userrole: 1

> {% 
  client.global.set("newUserStrId", response.body.str_id)
  client.test("Given logged in user, when getting user, should return 200 ok", function() {
    client.assert(response.status === 200, "Response status is not 200");
  })
%}

# Current logic does not allow to allow us to truly delete a user, so this test is commented out. 
# ###
# # @name Delete User (admin)
# # @import ./auth.http
# # @ref fintaryAdmin 
# DELETE http://localhost:3000/api/users
# Content-Type: application/json; charset=UTF-8
# Accept: application/json
# accountid: {{fintaryAdminAccountId}}
# authentication: Bearer {{fintaryAdminIdToken}}
# userrole: 1

# { 
#   "strId" : "k3haRt4wQnje28lEOGMV8" 
# }

# > {% 
#   client.test("Given admin credentials, when deleting the new user, should return 200 ok", function() {
#     client.assert(response.status === 200, "Response status is not 200");
#   })
# %}

# To be added in the upcoming integration tests improvement
###
# @name Update account user
# PATCH {{baseUrl}}/api/users/get_account_users?accId={{fintaryAdminAccountId}}
# Accept: */*
# Accept-Language: en-US,en;q=0.9
# Connection: keep-alive
# Content-Type: application/json
# Origin: http://localhost:3001
# Referer: http://localhost:3001/
# Sec-Fetch-Dest: empty
# Sec-Fetch-Mode: cors
# Sec-Fetch-Site: same-site
# User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
# accountid: {{fintaryAdminAccountId}}
# authentication: Bearer {{fintaryAdminIdToken}}
# feversion: 2025-06-09T18:31:12.191Z
# impuid: OBHLOkFEDlMlA6ym59s06fTHXt23
# sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
# sec-ch-ua-mobile: ?0
# sec-ch-ua-platform: "macOS"
# trace-id: yJdXK1aCKNQMm0Wp26ZRS
# userrole: 1
# x-timezone: America/Los_Angeles

# {
#   "id": 637,
#   "str_id": "tnmdTPOCqJAx9gakHkSCY",
#   "uid": "abTQMFTzbzMcw6NHYnyahM090B93",
#   "state": "active",
#   "created_at": "2024-12-25T03:58:24.420Z",
#   "created_by": "abTQMFTzbzMcw6NHYnyahM090B93",
#   "created_proxied_by": null,
#   "updated_at": "2025-06-10T19:15:40.684Z",
#   "updated_by": "OBHLOkFEDlMlA6ym59s06fTHXt23",
#   "updated_proxied_by": "8OnXo3dvlcRmLxp6H4cwxDsq2093",
#   "last_visited_at": "2025-06-06T06:24:48.721Z",
#   "company": null,
#   "drive_folder_name": null,
#   "email": "<EMAIL>",
#   "first_name": "Sharada",
#   "invite_token": null,
#   "last_drive_synced_time": null,
#   "last_name": "Glickman",
#   "last_uploaded_time": null,
#   "mode": null,
#   "notes": null,
#   "phone": null,
#   "plaid_access_token": null,
#   "reconciliation_view": null,
#   "refresh_token": null,
#   "sub_type": null,
#   "tos_accepted_at": "2025-03-11T14:18:59.383Z",
#   "type": null,
#   "user_contact": [],
#   "role_id": 1,
#   "invite_status": "active",
#   "invite_date": "2024-12-25T03:58:24.437Z",
#   "user_contact_id": "",
#   "account_id": "{{fintaryAdminAccountId}}"
# }

# > {% 
#   client.test("Given admin credentials, when updating account user, should return 2xx", function() {
#     client.assert(response.status >= 200 && response.status < 300, "Response status is not 2xx");
#   })
# %}
