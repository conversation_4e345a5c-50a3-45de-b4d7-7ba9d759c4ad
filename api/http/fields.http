###
# @name Fetch fields for processors 
# @import ./auth.http
# @ref fintaryAdmin 
GET {{baseUrl}}/api/fields?model=statements
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{fintaryAdminAccountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 

> {% 
  // client.test("Given processors, when fetching by type, should return at least one policies-report and one commissions-report", function() {
  //   client.assert(response.status === 200, "Response status is not 200");
  //   const policies = response.body.filter(p => p.type === "policies-report");
  //   const commissions = response.body.filter(p => p.type === "commissions-report");
  //   client.assert(policies.length > 0, "No policies-report processor found");
  //   client.assert(commissions.length > 0, "No commissions-report processor found");
  //   client.global.set("policyProcessorId", policies[0].str_id);
  //   client.global.set("commissionsProcessorId", commissions[0].str_id);
  // });
%}