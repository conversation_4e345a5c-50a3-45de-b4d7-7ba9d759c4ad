###
# @name Get all companies
# @import ./auth.http
# @ref fintaryAdmin 
GET {{baseUrl}}/api/companies
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{accountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 
impuid: {{uid}}

> {% 
  client.test("Given valid req, when fetching companies, should return 200 ok with data", function() {
    client.assert(response.status === 200, "Response status is not 200");
    client.assert(response.body.data.length > 0, "Response body is empty");
    const company = response.body.data[0];
    client.global.set("oldCompanyId", company.id)  
    client.global.set("oldCompanyStrId", company.str_id)
    client.assert(company.companies_processors.length === 0, "Company has processors despite no companies_processors relationship");
    })
%}

###
# @name Update pre-existing company with pre-existing processor
# @import ./auth.http
# @ref fintaryAdmin 
PATCH {{baseUrl}}/api/companies
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{accountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 
impuid: {{uid}}

{
  "id": {{oldCompanyId}},
  "company_name": "Tests Incorporated",
  "str_id": "{{oldCompanyStrId}}",
  "account_id": "{{accountId}}",
  "uid": "{{uid}}",
  "state": "active",
  "access": "account",
  "address": "123 Rainey Street",
  "alias_list": [],
  "canonical_id": null,
  "company_id": null,
  "company_name": "Corebridge",
  "config": null,
  "email": "<EMAIL>",
  "group_id": null,
  "log": null,
  "notes": "Test",
  "phone": "**********",
  "sync_id": null,
  "sync_worker": null,
  "type": ["Carrier"],
  "website": "",
  "companies_document_profiles": [],
  "processor_str_ids": ["7Jx_ucwSPYS-cnVHBu0kL"],
  "companies_processors": [
    {
      "processor_str_id": "7Jx_ucwSPYS-cnVHBu0kL",
      "import_status": "auto_import"
    }
  ],
  "document_profiles": [],
  "profile_str_ids": []
}

> {% 
  client.test("Given valid req, when updating company with processor, should return 200", function() {
    client.assert(response.status === 200, JSON.stringify(response, null, 2));
  }) 
%}

###
# @name Verify company has processor attached
# @import ./auth.http
# @ref fintaryAdmin 
GET {{baseUrl}}/api/companies
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{accountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 
impuid: {{uid}}

> {% 
  client.test("Given company with processor, when fetching companies, should return company with processor attached", function() {
    client.assert(response.status === 200, "Response status is not 200");
    const companyStrId = client.global.get("oldCompanyStrId");
    const company = response.body.data.find(c => c.str_id === companyStrId);
    client.assert(company, "Company not found in response");
    client.assert(company.companies_processors.length > 0, JSON.stringify(response, null, 2));
    client.assert(company.companies_processors[0].processor_str_id === "7Jx_ucwSPYS-cnVHBu0kL", "Wrong processor attached");
  }) 
%}

###
# @name Create a new company 
# @import ./auth.http
# @ref auth 
POST {{baseUrl}}/api/companies
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{accountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 
impuid: {{uid}}

{
  "company_name": "Tests Inc.",
  "email": "<EMAIL>",
  "website": "https://test.com",
  "phone": "**********",
  "address": "123 Rainey Street",
  "notes": "Just testing the create company function.",
  "type": [
    "Customer"
  ],
  "uid": "{{uid}}",
  "account_id": "{{accountId}}"
}

> {% 
  client.global.set("newCompanyId", response.body.id)  
  client.global.set("newCompanyStrId", response.body.str_id)  
  client.test("Given valid req, when creating company, should return 201 created", function() {
    client.assert(response.status === 201, "Response status is not 201");
    })
%}

###
# @name CompGrid Create
POST {{baseUrl}}/api/comp-grids
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{accountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 
impuid: {{uid}}

{
  "company_id": {{newCompanyId}}, 
  "name": "Test Comp Grid",
  "rate_fields": ["carrier_rate"],
  "notes": "Test Comp Grid Create",
  "uid": "{{uid}}",
  "account_id": "{{accountId}}"
}

> {% 
  client.global.set("compGridId", response.body.id)  
  client.global.set("compGridStrId", response.body.str_id)  
  client.test("Given valid req, when creating comp grid, should return 201 created", function() {
    client.assert(response.status === 201, "Response status is not 201");
    })
%}

###
# @name CompGrid Update 
PATCH {{baseUrl}}/api/comp-grids
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{accountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 
impuid: {{uid}}

{
  "id": {{compGridId}},
  "company_id": {{newCompanyId}},
  "name": "Test Comp Grid Updated",
  "uid": "{{uid}}",
  "account_id": "{{accountId}}"
}

> {% 
  client.test("Given valid req, when updating comp grid, should return 200 with update", function() {
    client.assert(response.status === 200, "Response status is not 200");
    client.assert(response.body.name === "Test Comp Grid Updated", "name does not match");
    }) 
%}

###
# @name CompGridProduct Create
POST {{baseUrl}}/api/comp-grids/products
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{accountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 
impuid: {{uid}}

{
  "comp_grid_id": {{compGridId}}, 
  "name": "Test Comp Grid Product",
  "company_product_ids": [],
  "notes": "Test Comp Grid Product Create",
  "type": "",
  "uid": "{{uid}}",
  "account_id": "{{accountId}}"
}

> {% 
  client.global.set("compGridProductId", response.body.id)  
  client.global.set("compGridProductStrId", response.body.str_id)  
  client.test("Given valid req, when creating comp grid product, should return 201 created", function() {
    client.assert(response.status === 201, "Response status is not 201");
    })
%}

###
# @name CompGridProduct Update 
PATCH {{baseUrl}}/api/comp-grids/products
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{accountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 
impuid: {{uid}}

{
  "id": {{compGridProductId}},
  "comp_grid_id": {{compGridId}},
  "company_product_ids": [{"id": null}],
  "name": "Test Comp Grid Updated",
  "uid": "{{uid}}",
  "account_id": "{{accountId}}"
}

> {% 
  client.test("Given valid req, when updating comp grid product, should return 200 with update", function() {
    client.assert(response.status === 200, "Response status is not 200");
    client.assert(response.body.name === "Test Comp Grid Updated", "name does not match");
    }) 
%}

###
# @name CompGridCriteria Create
# @import ./auth.http
# @ref auth
POST {{baseUrl}}/api/comp-grids/criteria
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{accountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 
impuid: {{uid}}

{
  "company_id": {{newCompanyId}},
  "comp_grid_id": {{compGridId}},
  "compensation_type": "",
  "compensation_type_alternative": "",
  "grid_product_id": {{compGridProductId}},
  "uid": "{{uid}}",
  "account_id": "{{accountId}}"
}

> {% 
  client.global.set("compGridCriterionId", response.body.id)  
  client.global.set("compGridCriterionStrId", response.body.str_id)  
  client.test("Given valid req, when updating comp grid criterion, should return 201 created", function() {
    client.assert(response.status === 201, "Response status is not 201");
  });
%}

###
# @name CompGridLevel Create
# @import ./auth.http
# @ref auth
POST {{baseUrl}}/api/comp-grids/levels
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{accountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 
impuid: {{uid}}

{
  "comp_grid_id": {{compGridId}},
  "name": "Test Comp Grid Level",
  "effective_date": "2024-09-16",
  "notes": "Test Create Comp Grid Level",
  "uid": "{{uid}}",
  "account_id": "{{accountId}}"
}

> {% 
  client.global.set("compGridLevelId", response.body.id)  
  client.global.set("compGridLevelStrId", response.body.str_id)  
  client.test("Given valid req, when updating comp grid level, should return 201 created", function() {
    client.assert(response.status === 201, "Response status is not 201");
  });
%}

###
# @name CompGridRate Create
# @import ./auth.http
# @ref auth
POST {{baseUrl}}/api/comp-grids/rates
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{accountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 
impuid: {{uid}}

{
  "comp_grid_id": {{compGridId}},
  "comp_grid_criterion_id": {{compGridCriterionId}},
  "carrier_rate": "20",
  "house_rate": "20",
  "rate": "20",
  "comp_grid_level_id": {{compGridLevelId}},
  "uid": "{{uid}}",
  "account_id": "{{accountId}}"
}

> {% 
  client.global.set("compGridRateId", response.body.id)  
  client.global.set("compGridRateStrId", response.body.str_id)  
  client.test("Given valid req, when creating comp grid rate, should return 201 created", function() {
    client.assert(response.status === 201, "Resposne status is not 201");
  });
%}

/*
UNFORTUNATELY BROKEN AS THIS PATCH IS COMPLICATED AND RELIES ON MANY NESTED OBJECTS - WILL REVISIT
@name Comp Grid Rate Update 
PATCH {{baseUrl}}/api/comp-grids/rates
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{accountId}} 
authentication: Bearer {{idToken}} 

{
  "id": {{compGridRateId}},
  "rate_fields": ["carrier_rate"],
  "carrier_rate": "30",
}

> {% 
  client.test("Given valid req, when updating company, should return 200 with update", function() {
    client.assert(response.status === 200, "Response status is not 200");
    client.assert(response.body.company_name === "Tests Incorporated", "company_name does not match");
    }) 
%}
*/

###
# @name CompGridRate Delete 
DELETE {{baseUrl}}/api/comp-grids/rates
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{accountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 
impuid: {{uid}}

{
  "ids": [{{compGridRateId}}],
  "uid": "{{uid}}",
  "strId": "{{compGridRateStrId}}",
  "account_id": "{{accountId}}"
}

> {% 
  client.test("Given valid req, when deleting comp grid rate, should return 200 deleted", function() {
    client.assert(response.status === 200, "Response status is not 200");
    client.assert(response.body.status === "OK", "Status is not OK");
  })
%}

###
# @name CompGridLevel Delete 
DELETE {{baseUrl}}/api/comp-grids/levels
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{accountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 
impuid: {{uid}}

{
  "ids": [{{compGridLevelId}}],
  "uid": "{{uid}}",
  "strId": "{{compGridLevelStrId}}",
  "account_id": "{{accountId}}"
}

> {% 
  client.test("Given valid req, when deleting comp grid level, should return 200 deleted", function() {
    client.assert(response.status === 200, "Response status is not 200");
    // client.assert(response.body.status === "OK", "Status is not OK"); // This endpoint returns "1" instead of "OK" for some unknown reason
  })
%}

###
# @name CompGridCriterion Delete 
DELETE {{baseUrl}}/api/comp-grids/criteria
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{accountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 
impuid: {{uid}}

{
  "ids": [{{compGridCriterionId}}],
  "uid": "{{uid}}",
  "strId": "{{compGridCriterionStrId}}",
  "account_id": "{{accountId}}"
}

> {% 
  client.test("Given valid req, when deleting comp grid criterion, should return 200 deleted", function() {
    client.assert(response.status === 200, "Response status is not 200");
    client.assert(response.body.status === "OK", "Status is not OK");
  })
%}

###
# @name CompGridProduct Delete 
DELETE {{baseUrl}}/api/comp-grids/products
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{accountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 
impuid: {{uid}}

{
  "ids": [{{compGridProductId}}],
  "uid": "{{uid}}",
  "strId": "{{compGridProductStrId}}",
  "account_id": "{{accountId}}"
}

> {% 
  client.test("Given valid req, when deleting comp grid product, should return 200 deleted", function() {
    client.assert(response.status === 200, "Response status is not 200");
    client.assert(response.body.status === "OK", "Status is not OK");
    })
%}

###
# @name CompGrid Delete 
DELETE {{baseUrl}}/api/comp-grids
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{accountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 
impuid: {{uid}}

{
  "ids": [{{compGridId}}],
  "uid": "{{uid}}",
  "strId": "{{compGridStrId}}",
  "account_id": "{{accountId}}"
}

> {% 
  client.test("Given valid req, when deleted comp grid, should return 200 deleted", function() {
    client.assert(response.status === 200, "Response status is not 200");
    client.assert(response.body.status === "OK", "Status is not OK");
    })
%}

###
# @name Company Delete
DELETE {{baseUrl}}/api/companies
Content-Type: application/json; charset=UTF-8
Accept: application/json
accountid: {{accountId}} 
authentication: Bearer {{fintaryAdminIdToken}} 
impuid: {{uid}}

{
  "ids": [{{newCompanyId}}]
}

> {% 
  client.test("Given valid req, when deleting company, should return 200 and confirm deletion", function() {
    client.assert(response.status === 200, "Response status is not 200");
    client.assert(response.body.status === "OK", "Status is not OK");
    }) 
%}

