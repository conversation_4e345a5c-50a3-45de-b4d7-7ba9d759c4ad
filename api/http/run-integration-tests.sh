#!/usr/bin/env bash

set -uo pipefail

temp_file=$(mktemp)
json_file=$(mktemp)
trap "rm -f $temp_file $json_file" EXIT

echo "Running httpyac tests..."

npx httpyac './http/*.http' --all --json > "$temp_file" 2>&1

echo "httpyac completed"
echo "File size: $(wc -c < "$temp_file") bytes"

echo "Extracting JSON from output..."
sed -n '/^{/,/^}$/p' "$temp_file" > "$json_file"

if [[ ! -s "$json_file" ]]; then
    echo "No JSON found in output, trying alternative extraction..."
    grep -A 999999 '^{' "$temp_file" | grep -B 999999 '^}$' > "$json_file"
fi

echo "JSON file size: $(wc -c < "$json_file") bytes"

if ! command -v jq &> /dev/null; then
  echo "❌ jq is required but not installed"
  exit 1
fi

json_input=$(cat "$json_file")

if [[ -z "$json_input" ]]; then
  echo "❌ No JSON input received"
  exit 1
fi

if ! echo "$json_input" | jq empty 2>/dev/null; then
  echo "❌ Invalid JSON input received"
  echo "JSON parse error - httpyac may have failed to generate valid output"
  exit 1
fi

requests_with_tests=$(echo "$json_input" | jq -c '.requests[]? | select(.testResults and (.testResults | length > 0))' 2>/dev/null)

if [[ -z "$requests_with_tests" ]]; then
  echo "❌ No requests with test results found"
  exit 1
fi

total_requests_with_tests=$(echo "$requests_with_tests" | wc -l)

echo ""
echo "📊 Test results summary:"
echo "Total requests with tests: $total_requests_with_tests"
echo ""
echo "=================================================="
echo ""

failed_tests=0
success_tests=0

while IFS= read -r request; do
  if [[ -z "$request" ]]; then
    continue
  fi
  
  request_name=$(echo "$request" | jq -r '.name // "Unknown"')
  
  has_failures=$(echo "$request" | jq -r '.testResults[]? | select(.status == "FAILED" or .status == "ERROR") | .status' | head -1)
  
  if [[ -n "$has_failures" ]]; then
    echo "❌ $request_name"
    ((failed_tests++))
    
    echo "$request" | jq -r '.testResults[]? | select(.status == "FAILED" or .status == "ERROR") | 
      if .error.displayMessage then 
        "   " + .error.displayMessage 
      else 
        "   " + .message 
      end'
    echo ""
  else
    echo "✅ $request_name"
    ((success_tests++))
  fi
done <<< "$requests_with_tests"

total_tests=$((success_tests + failed_tests))

echo ""
echo "=================================================="
echo "📈 Final summary:"
echo "✅ Passed: $success_tests"
echo "❌ Failed: $failed_tests"
echo "📊 Total: $total_tests"

if [[ $failed_tests -gt 0 ]]; then
  exit 1
else
  exit 0
fi
