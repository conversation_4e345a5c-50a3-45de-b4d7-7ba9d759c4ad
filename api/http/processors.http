###
# @name Create new processor
# @import ./auth.http
# @ref fintaryAdmin 
POST {{baseUrl}}/api/admin/processors
Content-Type: application/json
Accept: application/json
accountid: {{fintaryAdminAccountId}}
authentication: Bearer {{fintaryAdminIdToken}}

{
  "company_ids": [],
  "type": "policies-report",
  "name": "Test",
  "access": "global",
  "status": "draft",
  "notes": "",
  "inner_name": "",
  "owner": "{{fintaryAdminUid}}",
  "processor": "(json, libs) => {\n  return json;\n}",
  "document_str_id": "",
  "suggest_for": "",
  "uid": "{{fintaryAdminUid}}",
  "account_id": "{{fintaryAdminAccountId}}"
}

> {% 
  client.test("Given a processor, when creating, should return processor with expected fields", function() {
    client.assert(response.status === 201, "Response status is not 201 created");
    client.assert(response.body.id, "Processor id is missing");
    client.assert(response.body.name === "Test", "Processor name does not match");
    client.assert(response.body.processor.includes("return json;"), "Processor code does not match");
    client.global.set("procId", response.body.id);
    client.global.set("procCompanyId", response.body.company_id);
    client.global.set("procType", response.body.type);
    client.global.set("procName", response.body.name);
    client.global.set("procAccess", response.body.access);
    client.global.set("procStatus", response.body.status);
    client.global.set("procNotes", response.body.notes);
    client.global.set("procInnerName", response.body.inner_name);
    client.global.set("procOwner", response.body.owner);
    client.global.set("procProcessor", response.body.processor);
    client.global.set("procDocumentStrId", response.body.document_str_id);
    client.global.set("procSuggestFor", response.body.suggest_for);
    client.global.set("procUid", response.body.uid);
    client.global.set("procAccountId", response.body.account_id);
  });
%}

###
# @name Update processor
# @import ./auth.http
# @ref fintaryAdmin 
PATCH {{baseUrl}}/api/admin/processors
Content-Type: application/json
Accept: application/json
accountid: {{fintaryAdminAccountId}}
authentication: Bearer {{fintaryAdminIdToken}}

{
  "company_id": "{{procCompanyId}}",
  "company_ids": [],
  "type": "{{procType}}",
  "name": "Global Policies Processor",
  "access": "{{procAccess}}",
  "status": "{{procStatus}}",
  "notes": "",
  "inner_name": "{{procInnerName}}",
  "owner": "{{procOwner}}",
  "processor": "(json) => {\n  console.log('test')\n  return json;\n}",
  "document_str_id": "{{procDocumentStrId}}",
  "suggest_for": "{{procSuggestFor}}",
  "id": "{{procId}}",
  "updated_by": "{{procOwner}}",
  "uid": "{{procUid}}",
  "account_id": "{{procAccountId}}"
}

> {% 
  client.test("Given a processor, when updating, should return updated processor with expected fields", function() {
    client.assert(response.status === 200, `Response status is not 200: ${response.body}, ${response.error}`);
    client.assert(response.body.processor.includes("console.log('test')"), "Processor code was not updated");
    client.assert(response.body.status === client.global.get("procStatus"), "Processor status does not match");
    client.assert(response.body.type === client.global.get("procType"), "Processor type does not match");
    client.assert(response.body.account_id === client.global.get("procAccountId"), "Processor account_id does not match");
  });
%}

###
# @name Delete processor
# @import ./auth.http
# @ref fintaryAdmin 
DELETE {{baseUrl}}/api/admin/processors
Content-Type: application/json
Accept: application/json
accountid: {{fintaryAdminAccountId}}
authentication: Bearer {{fintaryAdminIdToken}}

{
  "ids": [{{procId}}],
  "uid": "{{procUid}}",
  "account_id": "{{procAccountId}}"
}

> {% 
  client.test("Given a processor, when deleting, should return status OK", function() {
    client.assert(response.status === 200, "Response status is not 200");
    client.assert(response.body.status === "OK", "Delete status is not OK");
  });
%}

###
# @name Get processors as fintary admin
# @import ./auth.http
# @ref fintaryAdmin
GET {{baseUrl}}/api/admin/processors?type=policies-report&status=approved
Accept: application/json
accountid: {{fintaryAdminAccountId}}
authentication: Bearer {{fintaryAdminIdToken}}

> {% 
  client.test("Given admin, should return all unrestricted processor fields", function() {
    client.assert(response.status === 200, "Response status is not 200");
    client.assert(Array.isArray(response.body), "Response body is not an array");
    if (response.body.length > 0) {
      const processor = response.body[0];
      // Unrestricted fields for admin
      [
        "id", "str_id", "name", "documents", "access", "company_id", "created_at", "created_by",
        "document_str_id", "extractionsid", "file_type", "inner_name", "method", "notes",
        "processor", "processor_status", "reviewed_at", "reviewed_by", "reviewer_id",
        "reviewer_str_id", "state", "status", "owner", "type", "updated_at", "updated_by",
        "created_proxied_by", "suggest_for", "extractions", "users_processors_created_byTousers",
        "users_processors_updated_byTousers", "users_processors_reviewed_byTousers", "companies_processors"
      ].forEach(field => {
        client.assert(
          field in processor,
          `Admin should see field "${field}" in processor response`
        );
      });
    }
  });
%}

###
# @name Get processors as user
# @import ./auth.http
# @ref auth 
GET {{baseUrl}}/api/processors?type=policies-report&status=approved
Accept: application/json
accountid: {{accountId}}
authentication: Bearer {{idToken}}

> {% 
  client.test("Given user, should only return identifier processor fields", function() {
    client.assert(response.status === 200, "Response status is not 200");
    client.assert(Array.isArray(response.body), "Response body is not an array");
    if (response.body.length > 0) {
      const processor = response.body[0];
      const allowedFields = ["id", "str_id", "name", "type", "documents", "inner_name", "status", "access", "method"];
      Object.keys(processor).forEach(field => {
        client.assert(
          allowedFields.includes(field),
          `User should not see field "${field}" in processor response, got "${field}" ${JSON.stringify(response.body) }`
        );
      });
    }
  });
%}

