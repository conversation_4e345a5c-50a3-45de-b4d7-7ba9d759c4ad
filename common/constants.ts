export const dashboardWidgetOptions = {
  policiesInForce: 'policiesInForce',
  premiumsInForce: 'premiumsInForce',
  premiumsIssuePay: 'premiumsIssuePay',
  commissionsReceived: 'commissionsReceived',
  commissionsPaid: 'commissionsPaid',
  numPoliciesWithCommissions: 'numPoliciesWithCommissions',
  policiesWithReversedPayments: 'policiesWithReversedPayments',
  commissionsByCarrierMap: 'commissionsByCarrierMap',
  commissionsByWritingCarrierMap: 'commissionsByWritingCarrierMap',
  premiumsByCarrierMap: 'premiumsByCarrierMap',
  agentCommissions: 'agentCommissions',
  commissionsByTimeMap: 'commissionsByTimeMap',
  commissionsAccByTimeMap: 'commissionsAccByTimeMap',
  commissionsOutByTimeMap: 'commissionsOutByTimeMap',
  commissionsOutAccByTimeMap: 'commissionsOutAccByTimeMap',
  commissionsNetByTimeMap: 'commissionsNetByTimeMap',
  commissionsNetAccByTimeMap: 'commissionsNetAccByTimeMap',
  policiesByTimeMap: 'policiesByTimeMap',
  effectiveCommissionRateByCarrierArray:
    'effectiveCommissionRateByCarrierArray',
  effectiveCommissionRateByWritingCarrierArray:
    'effectiveCommissionRateByWritingCarrierArray',
};

export const e2eTestWidgetName = 'E2e test widget';

export const CALC_METHODS = {
  compGrid: 'Pay commission to grid level',
  compGridLevel: 'Share override to grid level',
  payoutRate: 'Pay a set rate',
  payHouseRate: 'Pay house rate',
  payOverrideUpToTotalRate: 'Pay override up to total rate',
  keepRate: 'Keep a set rate',
  overrideSplit: 'Split percentage',
  carrierGridSplitRemainder: 'Pay to a grid level then split percentage',
  payoutRateIncentiveTiers: 'Pay based on incentive tier',
  referral: 'Referral',
};

export enum WidgetGroup {
  BOX = 'BOX',
  CHART = 'CHART',
  TABLE = 'TABLE',
}

export const FieldTypes = {
  BOOLEAN: 'boolean',
  CODE: 'code',
  CODEMIRROR: 'codemirror',
  CURRENCY: 'currency',
  CUSTOM: 'custom',
  DATE: 'date',
  DATE_RANGE: 'date_range',
  DIVIDER: 'divider',
  DYNAMIC_SELECT: 'dynamic-select',
  FIELD_MATCHER: 'field-matcher',
  HEADING: 'heading',
  INTEGER: 'integer',
  PERCENTAGE: 'percentage',
  RATE_SCHEDULE: 'rate-schedule-annual',
  SELECT: 'select',
  DRAGGABLE_SELECT: 'draggable-select',
  SUB_HEADING: 'sub-heading',
  TEXT: 'text',
  STRING_ARRAY: 'string-array',
  TABLE_CELL_CUSTOM: 'table-cell-custom',
};

export enum AccountIds {
  BROKERS_ALLIANCE = '8LKborwusmH-8C79qmpfx',
  EDISON_RISK = 'tY4K6TGT8NH6yMREQf2XQ',
  FINTARY_GLOBAL = '8cb1c18c05062d1134db',
  QUILITY = '_xNo3yr3mHsZZHeubkE7h',
  RISK_TAG = 'W4kSrayZvmh26pGfYVrGE',
  TRAILSTONE = 'f_mX8g_LfFzV1SeD-U4ki',
  TRANSGLOBAL = 'XN9U5UtGrD5aovyEwNlHj',
  WORLD_CHANGERS = 'LTVy72chvj8LvP_ztKSax',
  BUDDYINS = '_EEW0QFevUkNwWoMzFruc',
  BGA = 'rMUK5nkQ29MwiOS3hfb-j',
  ALLIED = 'mPpHFihSyTTHfwqb01Cvp',
  BROKERS_CENTRAL = 'UiS5IAWiCLDmsTrhE4BRc',
  DMI = 'EoSqZ1CPSt0GX56Eqt-Sx',
}

export const ALEVO_EXCLUDED_AGENTS = [
  'DrRNXzr0itzLiqYRJyVkk',
  'Jcp0FdGNQR6Gk6UnoYFAB',
  'SwHJMuUejInJGRGlHXatD',
  'zbmNbWcwgM1gNEvLH_7n0',
];

export enum WorkerNames {
  AgencyIntegratorWorker = 'AgencyIntegratorWorker',
  NowCertsWorker = 'NowCertsWorker',
  OneHQWorker = 'OneHQWorker',
  SmartOfficeWorker = 'SmartOfficeWorker',
  BenefitPointWorker = 'BenefitPointWorker',
  TransGlobalWorker = 'TransGlobalWorker',
  MyAdvisorGridsWorker = 'MyAdvisorGridsWorker',
  AwsS3Worker = 'AwsS3Worker',
  DocumentProcessingWorker = 'DocumentProcessingWorker',
  // Sync data using fintary openapi
  OpenAPI = 'OpenAPI',
}

export const RATES_LABELS = {
  carrier_rate: 'Carrier rate',
  house_rate: 'House rate',
  rate: 'Total rate',
};

export const transactionTypes = {
  COMP_REPORT_PAYMENT: 'comp_report_payment',
};

export const HTTP_HEADERS = {
  TIMEZONE: 'x-timezone',
  AUTHENTICATION: 'authentication',
  FE_VERSION: 'feversion',
  TRACE_ID: 'trace-id',
  ACCOUNT_ID: 'accountid',
  IMPUID: 'impuid',
} as const;

export enum ResponseAction {
  LOG_OUT = 'logout',
}

export const PAYMENT_METHODS = ['ACH', 'CHECK', 'WIRE', 'ADJUSTMENT'] as const;

export const MATH_MAGIC_NUMBERS = {
  ADD_IDENTITY: 0,
  DIVIDE_IDENTITY: 1,
  MULTIPLY_IDENTITY: 1,
  PERCENTAGE_IDENTITY: 100,
  WHOLE_BASE: 1,
};

export const DEFAULT_FILTER = {
  BLANK_OPTION: '(Blank)',
  BLANK_VALUE: 'blank',
};
